import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { Search, XCircle, CreditCard, Hash, Shield, LineChart, ChevronDown, User, Wallet, AlertTriangle, MoreVertical, Eye, EyeOff, Image } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { EditOrderModal } from "@/components/edit-order-modal"
import { OrderStatus, OrderType, RejectReasons, FailReasons } from "@/types/order"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { ViewOrderModal } from "@/components/view-order-modal"
import { FailOrderModal } from "@/components/fail-order-modal"
import { PassOrderModal } from "@/components/pass-order-modal"

interface AdminTablesProps {
  selectedSection: "users" | "orders" | "completedOrders" | "failedOrders" | "passOrders" | "stageTwoOrders" | "liveOrders" | "runningOrders" | "analytics" | "reports" | null;
}

interface RunningOrder {
  id: string;
  order_id: string;
  platform_login: string;
  platform_password: string;
  server: string;
  session_id: string;
  terminal_id: string;
  txid?: string;
  image?: {
    image_url: string;
    created_at: string;
  };
}

interface User {
  id: number;
  username: string;
  email: string;
  name: string;
  country: string;
  phone_no: string;
  address: string;
  hashed_password: string;
  createdAt: string;
}

interface OrderDetails {
  id: number | string;
  user: {
    name: string;
    email: string;
    hashed_password?: string;
  };
  amount: string;
  status: OrderStatus;
  type: OrderType;
  createdAt: string;
  accountType: string;
  platformType: string;
  platformLogin?: string;
  platformPassword?: string;
  server?: string;
  startingBalance?: number;
  currentBalance?: number;
  profitTarget?: number;
  paymentProof?: string;
  paymentMethod?: string;
  txid?: string;
  order_id?: string;
  sessionId?: string;
  terminalId?: string;
  image?: {
    image_url: string;
    created_at: string;
  };
}

export function AdminTables({ selectedSection }: AdminTablesProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [orders, setOrders] = useState<OrderDetails[]>([])
  const [completedOrders, setCompletedOrders] = useState<OrderDetails[]>([])
  const [failedOrders, setFailedOrders] = useState<OrderDetails[]>([])
  const [passOrders, setPassOrders] = useState<OrderDetails[]>([])
  const [stageTwoOrders, setStageTwoOrders] = useState<OrderDetails[]>([])
  const [liveOrders, setLiveOrders] = useState<OrderDetails[]>([])
  const [runningOrders, setRunningOrders] = useState<RunningOrder[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [editedOrder, setEditedOrder] = useState<OrderDetails | null>(null)
  const [isMobile, setIsMobile] = useState(false)
  const [viewOrder, setViewOrder] = useState<OrderDetails | null>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleConfirmOrder = async (order: OrderDetails) => {
    try {
      // ... existing confirm order logic ...
    } catch (error) {
      console.error('Error confirming order:', error)
    }
  }

  const handleSaveChanges = async (order: OrderDetails) => {
    try {
      // Implement save changes logic
      console.log('Saving changes for order:', order);
    } catch (error) {
      console.error('Error saving changes:', error);
    }
  };

  const handleRejectOrder = async (order: OrderDetails, reason: RejectReasons) => {
    try {
      const formData = new FormData();
      formData.append('reason', reason);
      const response = await fetch(`https://fundedhorizon-back-65a0759eedf9.herokuapp.com/order/reject_order/${order.id}`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        console.log(`Order ${order.id} rejected due to: ${reason}`);
      } else {
        throw new Error('Failed to reject order');
      }
    } catch (error) {
      console.error('Error rejecting order:', error);
    }
  };

  const handleFailOrder = async (order: OrderDetails, reason: FailReasons) => {
    try {
      const formData = new FormData();
      formData.append('reason', reason);
      formData.append('date', new Date().toISOString());
      
      const response = await fetch(`https://fundedhorizon-back-65a0759eedf9.herokuapp.com/order/fail_order/${order.order_id?.replace('FDH', '')}`, {
        method: "POST",
        body: formData
      });

      if (!response.ok) {
        throw new Error("Failed to fail order");
      }

      console.log("Order failed successfully");
    } catch (error) {
      console.error("Error failing order:", error);
    }
  };

  const handleViewOrder = (order: OrderDetails) => {
    setViewOrder(order);
  };

  const renderOrderActions = (order: OrderDetails) => {
    if (!mounted) return null;

    return (
      <div className="flex items-center gap-2">
        {selectedSection === "orders" && (
          <>
            <EditOrderModal
              order={order}
              onSave={handleSaveChanges}
              onReject={handleRejectOrder}
              onFail={handleFailOrder}
            />
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => handleConfirmOrder(order)}
              className="bg-blue-500 text-white hover:bg-blue-600 rounded-lg text-xs"
            >
              Confirm
            </Button>
          </>
        )}
        <ViewOrderModal
          order={order}
          onView={handleViewOrder}
          status={order.status}
        />
      </div>
    );
  };

  const itemsPerPage = 10;
  const [currentPage, setCurrentPage] = useState(1);

  const getPaginatedData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    
    switch (selectedSection) {
      case "users":
        return users.slice(startIndex, endIndex);
      case "runningOrders":
        return runningOrders.slice(startIndex, endIndex);
      case "orders":
        return orders.slice(startIndex, endIndex);
      case "completedOrders":
        return completedOrders.slice(startIndex, endIndex);
      case "failedOrders":
        return failedOrders.slice(startIndex, endIndex);
      case "passOrders":
        return passOrders.slice(startIndex, endIndex);
      case "stageTwoOrders":
        return stageTwoOrders.slice(startIndex, endIndex);
      case "liveOrders":
        return liveOrders.slice(startIndex, endIndex);
      default:
        return [];
    }
  };

  const paginatedData = getPaginatedData();

  const renderPaginationControls = () => {
    if (!mounted) return null;

    const totalPages = Math.ceil(
      (selectedSection === "users" ? users.length :
       selectedSection === "runningOrders" ? runningOrders.length :
       selectedSection === "orders" ? orders.length :
       selectedSection === "completedOrders" ? completedOrders.length :
       selectedSection === "failedOrders" ? failedOrders.length :
       selectedSection === "passOrders" ? passOrders.length :
       selectedSection === "stageTwoOrders" ? stageTwoOrders.length :
       selectedSection === "liveOrders" ? liveOrders.length : 0) / itemsPerPage
    );

    return (
      <div className="flex justify-between items-center mt-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
          className="text-xs"
        >
          Previous
        </Button>
        <span className="text-xs">
          Page {currentPage} of {totalPages}
        </span>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
          disabled={currentPage === totalPages}
          className="text-xs"
        >
          Next
        </Button>
      </div>
    );
  };

  const renderTable = () => {
    if (!mounted) return null;

    switch (selectedSection) {
      case "users":
        // ... existing users table code ...
        return null;
      case "runningOrders":
        // ... existing running orders table code ...
        return null;
      case "orders":
      case "completedOrders":
      case "failedOrders":
      case "passOrders":
      case "stageTwoOrders":
      case "liveOrders":
        return (
          <div className="overflow-x-auto">
            <div className="mb-4">
              <Input
                placeholder="Search by name, email, payment method, transaction ID, or order ID..."
                value={searchTerm}
                onChange={handleSearch}
                className="max-w-sm bg-[#1E3A5F]/20 border-[#1E3A5F] text-white text-xs"
              />
            </div>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-xs">Order ID</TableHead>
                  <TableHead className="text-xs">Name</TableHead>
                  <TableHead className="text-xs">Account Type</TableHead>
                  <TableHead className="text-xs">Amount</TableHead>
                  <TableHead className="text-xs">Platform</TableHead>
                  <TableHead className="text-xs">Transaction ID</TableHead>
                  <TableHead className="text-xs">Status</TableHead>
                  {!isMobile && <TableHead className="text-xs">Created At</TableHead>}
                  <TableHead className="text-xs">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedData.map((order: OrderDetails) => (
                  <TableRow key={order.id}>
                    <TableCell className="text-xs font-medium">{order.id}</TableCell>
                    <TableCell className="text-xs">{order.user.name}</TableCell>
                    <TableCell className="text-xs">{order.accountType}</TableCell>
                    <TableCell className="text-xs">${order.amount}</TableCell>
                    <TableCell className="text-xs">{order.platformType}</TableCell>
                    <TableCell className="text-xs">{order.txid}</TableCell>
                    <TableCell className="text-xs">{order.status}</TableCell>
                    {!isMobile && <TableCell className="text-xs">{order.createdAt}</TableCell>}
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {selectedSection === "orders" && (
                          <>
                            <EditOrderModal
                              order={order}
                              onSave={handleSaveChanges}
                              onReject={handleRejectOrder}
                              onFail={handleFailOrder}
                            />
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleConfirmOrder(order)}
                              className="bg-blue-500 text-white hover:bg-blue-600 rounded-lg text-xs"
                            >
                              Confirm
                            </Button>
                          </>
                        )}
                        <ViewOrderModal
                          order={order}
                          onView={handleViewOrder}
                          status={order.status}
                        />
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
            {renderPaginationControls()}
          </div>
        );
      default:
        return null;
    }
  };

  if (!mounted) {
    return null;
  }

  return (
    <motion.div
      className="space-y-8"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle className="text-xs">
            {selectedSection ? selectedSection.charAt(0).toUpperCase() + selectedSection.slice(1) : "Select a section"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {renderTable()}
        </CardContent>
      </Card>
    </motion.div>
  );
} 