"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { Inter } from "next/font/google"
import { CustomToaster } from "@/components/ui/custom-toaster"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    setIsClient(true)
    const token = localStorage.getItem("admin-token")
    setIsAuthenticated(token === "abut")
    
    if (token !== "abut") {
      router.push("/login")
    }
  }, [router])

  // Only render children when we're on the client and authenticated
  if (!isClient) {
    return <div className="min-h-screen bg-[#0a1929] flex items-center justify-center">
      <div className="animate-pulse text-white">Loading...</div>
    </div>
  }

  if (!isAuthenticated) {
    return <div className="min-h-screen bg-[#0a1929] flex items-center justify-center">
      <div className="text-white">Authenticating...</div>
    </div>
  }

  return (
    <html lang="en" className="dark">
      <body className={inter.className}>
        <div className="min-h-screen bg-[#0a1929]">
          {/* Main Content Area */}
          <div className="relative min-h-screen w-full">
            {/* Background Effects */}
            <div className="absolute inset-0">
              <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5" />
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-orange-500/5 blur-3xl" />
            </div>
            
            {/* Content */}
            <div className="relative z-10">
              {children}
            </div>
          </div>
          <CustomToaster />
        </div>
      </body>
    </html>
  )
}
