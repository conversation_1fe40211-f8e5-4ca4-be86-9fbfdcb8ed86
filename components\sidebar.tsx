"use client"

import { useState, useEffect, useRef } from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { 
  LayoutDashboard, 
  Users, 
  FileCheck, 
  CreditCard, 
  LogOut, 
  Menu, 
  ShoppingCart,
  Settings,
  User,
  History,
  BookOpen,
  BarChart3,
  Shield,
  Bell,
  HelpCircle,
  Award,
  Sparkles,
  Zap,
  TrendingUp,
  Coins,
  Gift
} from "lucide-react"
import { motion } from "framer-motion"

const dashboardItems = [
  { 
    icon: LayoutDashboard, 
    label: "Dashboard", 
    href: "/dashboard",
    description: "Trading overview and analytics",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: ShoppingCart, 
    label: "Buy Challenge", 
    href: "/dashboard/buy",
    description: "Purchase trading challenges",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: BarChart3, 
    label: "Order History", 
    href: "/dashboard/order-history",
    description: "View all your trading orders",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: Award, 
    label: "Certificates", 
    href: "/dashboard/certificates",
    description: "Manage and verify certificates",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: Users, 
    label: "Referrals", 
    href: "/dashboard/referal",
    description: "Manage your referral network",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: Coins, 
    label: "Earn Coin", 
    href: "/dashboard/earn-coin",
    description: "Earn and manage your coins",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: Gift, 
    label: "Giveaway", 
    href: "/dashboard/giveaway",
    description: "Participate in giveaways",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: FileCheck, 
    label: "KYC Verification", 
    href: "/dashboard/kyc",
    description: "Complete identity verification",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: CreditCard, 
    label: "Withdrawals", 
    href: "/dashboard/withdraw",
    description: "Withdraw your profits",
    gradient: "from-blue-500 to-blue-600"
  },
  { 
    icon: User, 
    label: "Profile", 
    href: "/dashboard/profile",
    description: "Manage your account settings",
    gradient: "from-orange-500 to-orange-600"
  },
  { 
    icon: BookOpen, 
    label: "Rules & Guidelines", 
    href: "/dashboard/rules",
    description: "Trading rules and policies",
    gradient: "from-blue-500 to-blue-600"
  }
]

const adminItems = [
  {
    icon: Settings,
    label: "Admin Portal",
    href: "/adminportal",
    description: "Administrative dashboard",
    gradient: "from-orange-500 to-orange-600"
  }
]

export function Sidebar() {
  const pathname = usePathname()
  const router = useRouter()
  const [isMobile, setIsMobile] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [isHovered, setIsHovered] = useState<string | null>(null)
  const [userInfo, setUserInfo] = useState<any>(null)
  const navScrollRef = useRef<HTMLDivElement>(null)

  const isAdminPortal = pathname.startsWith('/adminportal')
  const sidebarItems = isAdminPortal ? adminItems : dashboardItems
  const sidebarWidth = isAdminPortal ? 'w-64' : 'w-80'

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [])

  useEffect(() => {
    // Get user info from localStorage or API
    const token = localStorage.getItem('access_token')
    if (token) {
      // You can fetch user info here if needed
      setUserInfo({ name: "Trader", email: "<EMAIL>" })
    }
  }, [])

  // Preserve scroll position
  useEffect(() => {
    const nav = navScrollRef.current
    if (!nav) return
    // Restore scroll position if saved
    const saved = sessionStorage.getItem('sidebar-scroll')
    if (saved) nav.scrollTop = parseInt(saved, 10)
    // Save scroll position on unmount
    const handle = () => sessionStorage.setItem('sidebar-scroll', String(nav.scrollTop))
    nav.addEventListener('scroll', handle)
    return () => {
      nav.removeEventListener('scroll', handle)
      sessionStorage.setItem('sidebar-scroll', String(nav.scrollTop))
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem('access_token')
    localStorage.removeItem('token_type')
    router.push('/sigin')
  }

  const SidebarContent = () => {
    return (
      <div className="flex flex-col h-full bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-500/5 via-orange-500/5 to-blue-500/5"></div>
          <div className="absolute top-20 left-20 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl animate-pulse"></div>
          <div className="absolute bottom-20 right-20 w-40 h-40 bg-orange-500/10 rounded-full blur-2xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-blue-500/5 to-orange-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
        </div>

        {/* Header */}
        <div className="relative p-6 border-b border-slate-700/50">
          <Link 
            href={isAdminPortal ? "/adminportal" : "/dashboard"} 
            className="flex items-center gap-3 group"
          >
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 via-orange-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300 transform group-hover:scale-105">
                <Shield className="w-7 h-7 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full border-2 border-slate-900 animate-pulse"></div>
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-slate-100 via-blue-100 to-orange-100 bg-clip-text text-transparent group-hover:from-blue-200 group-hover:via-orange-200 group-hover:to-blue-200 transition-all duration-300">
                Funded Horizon
              </h1>
              <p className="text-xs text-slate-400 font-medium group-hover:text-slate-300 transition-colors duration-300">Professional Trading</p>
            </div>
          </Link>
        </div>

        {/* User Info */}
        {userInfo && !isAdminPortal && (
          <div className="relative p-4 border-b border-slate-700/50">
            <div className="flex items-center gap-3 p-4 rounded-xl bg-gradient-to-r from-slate-800/50 to-slate-700/50 border border-slate-600/50 backdrop-blur-sm hover:from-slate-700/50 hover:to-slate-600/50 transition-all duration-300 group">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 via-orange-500 to-blue-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-orange-500 rounded-full border-2 border-slate-800 animate-pulse"></div>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold text-slate-200 truncate group-hover:text-slate-100 transition-colors duration-300">{userInfo.name}</p>
                <p className="text-xs text-slate-400 truncate group-hover:text-slate-300 transition-colors duration-300">{userInfo.email}</p>
                <div className="flex items-center gap-1 mt-1">
                  <Sparkles size={12} className="text-orange-400" />
                  <span className="text-xs text-orange-400 font-medium">Premium Member</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav
          ref={navScrollRef}
          className="flex-1 p-4 space-y-1 overflow-y-auto scrollbar-thin scrollbar-thumb-slate-600 scrollbar-track-slate-800"
        >
          {sidebarItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              onMouseEnter={() => setIsHovered(item.href)}
              onMouseLeave={() => setIsHovered(null)}
              className="relative block group"
            >
              <div
                className={cn(
                  "w-full flex items-center gap-2 px-3 py-2.5 rounded-lg transition-all duration-300 relative overflow-hidden backdrop-blur-sm",
                  pathname === item.href
                    ? "bg-gradient-to-r from-blue-600/20 via-orange-600/20 to-blue-600/20 text-blue-100 border border-blue-500/30 shadow-lg shadow-blue-500/10 transform scale-[1.02]"
                    : "text-slate-300 hover:bg-gradient-to-r hover:from-slate-700/50 hover:to-slate-600/50 hover:text-slate-100 hover:border-slate-600/30 border border-transparent hover:transform hover:scale-[1.02]",
                  isHovered === item.href && "scale-[1.02] transform"
                )}
              >
                {/* Background gradient on hover */}
                <div className={cn(
                  "absolute inset-0 bg-gradient-to-r opacity-0 transition-opacity duration-300",
                  `from-${item.gradient.split('-')[1]}-500/10 to-${item.gradient.split('-')[3]}-500/10`,
                  isHovered === item.href && "opacity-100"
                )} />
                
                {/* Icon with gradient background */}
                <div className={cn(
                  "relative z-10 p-1.5 rounded-md transition-all duration-300",
                  pathname === item.href 
                    ? `bg-gradient-to-br ${item.gradient} shadow-lg`
                    : `bg-slate-700/50 group-hover:bg-gradient-to-br group-hover:${item.gradient} group-hover:shadow-lg`
                )}>
                  <item.icon className={cn(
                    "h-4 w-4 transition-all duration-300",
                    pathname === item.href 
                      ? "text-white" 
                      : "text-slate-400 group-hover:text-white",
                    isHovered === item.href && "scale-110"
                  )} />
                </div>
                
                <div className="flex-1 relative z-10 min-w-0">
                  <span className="font-medium text-xs tracking-wide truncate">{item.label}</span>
                  {isHovered === item.href && (
                    <motion.p 
                      initial={{ opacity: 0, y: -5 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-xs text-slate-400 mt-0.5 truncate"
                    >
                      {item.description}
                    </motion.p>
                  )}
                </div>
                
                {pathname === item.href && (
                  <motion.div 
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute right-2 w-1 h-4 rounded-full bg-gradient-to-b from-blue-400 to-orange-400 shadow-lg"
                  />
                )}
                
                {/* Hover indicator */}
                {isHovered === item.href && pathname !== item.href && (
                  <motion.div 
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute right-2 w-1 h-4 rounded-full bg-gradient-to-b from-slate-400 to-slate-500"
                  />
                )}
              </div>
            </Link>
          ))}
        </nav>

        {/* Footer */}
        <div className="relative p-3 border-t border-slate-700/50 space-y-2">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-2">
            <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500/10 to-blue-600/10 border border-blue-500/20">
              <div className="flex items-center gap-1">
                <TrendingUp size={12} className="text-blue-400" />
                <span className="text-xs text-blue-400 font-medium">+12.5%</span>
              </div>
              <p className="text-xs text-slate-400 mt-0.5">This Week</p>
            </div>
            <div className="p-2 rounded-lg bg-gradient-to-r from-orange-500/10 to-orange-600/10 border border-orange-500/20">
              <div className="flex items-center gap-1">
                <Zap size={12} className="text-orange-400" />
                <span className="text-xs text-orange-400 font-medium">24/7</span>
              </div>
              <p className="text-xs text-slate-400 mt-0.5">Support</p>
            </div>
          </div>

          {/* Help & Support */}
          <Link
            href="/dashboard/help"
            className="flex items-center gap-2 px-3 py-2 text-slate-400 hover:text-slate-200 hover:bg-gradient-to-r hover:from-slate-700/50 hover:to-slate-600/50 rounded-lg transition-all duration-300 group"
          >
            <div className="p-1 rounded-lg bg-gradient-to-br from-blue-500/15 to-orange-500/15 group-hover:from-blue-500/25 group-hover:to-orange-500/25 transition-all duration-300">
              <HelpCircle size={14} className="text-blue-400 group-hover:scale-110 transition-transform duration-300" />
            </div>
            <span className="text-xs font-medium">Help & Support</span>
          </Link>
          
          {/* Logout */}
          <button
            onClick={handleLogout}
            className="w-full flex items-center gap-2 px-3 py-2 text-left text-slate-400 hover:bg-gradient-to-r hover:from-orange-500/10 hover:to-orange-600/10 hover:text-orange-400 rounded-lg transition-all duration-300 group border border-transparent hover:border-orange-500/20"
          >
            <div className="p-1 rounded-lg bg-gradient-to-br from-orange-500/15 to-orange-600/15 group-hover:from-orange-500/25 group-hover:to-orange-600/25 transition-all duration-300">
              <LogOut size={14} className="group-hover:rotate-12 transition-transform duration-300" />
            </div>
            <span className="font-medium text-xs">Sign Out</span>
          </button>
        </div>
      </div>
    )
  }

  if (isMobile) {
    return (
      <>
        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="icon"
          className="fixed top-4 left-4 z-50 md:hidden bg-slate-800/90 backdrop-blur-sm border border-slate-700/50 hover:bg-slate-700/90 shadow-lg"
          onClick={() => setIsOpen(true)}
        >
          <Menu className="h-5 w-5 text-slate-200" />
        </Button>

        {/* Mobile Drawer */}
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetContent side="left" className={`${sidebarWidth} p-0 bg-slate-900 border-r border-slate-700/50`}>
            <SidebarContent />
          </SheetContent>
        </Sheet>

        {/* Desktop Sidebar */}
        <div className={`hidden md:block fixed inset-y-0 left-0 ${sidebarWidth} bg-slate-900 border-r border-slate-700/50 shadow-2xl`}>
          <SidebarContent />
        </div>
      </>
    )
  }

  return (
    <div className={`hidden md:block fixed left-0 top-0 h-screen ${sidebarWidth} bg-slate-900 text-slate-200 border-r border-slate-700/50 shadow-2xl`}>
      <SidebarContent />
    </div>
  )
}
