"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Users, 
  UserPlus, 
  TrendingUp, 
  DollarSign, 
  Copy, 
  Share2, 
  Award,
  Calendar,
  CheckCircle,
  Clock,
  Star,
  Gift,
  BarChart3,
  Target,
  Zap
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { toast } from "react-toastify"

interface Referral {
  id: string
  name: string
  email: string
  status: 'pending' | 'active' | 'completed' | 'inactive'
  joinDate: string
  lastActivity: string
  totalEarnings: number
  commission: number
  level: 'bronze' | 'silver' | 'gold' | 'platinum'
  avatar: string
}

interface ReferralStats {
  totalReferrals: number
  activeReferrals: number
  totalEarnings: number
  thisMonthEarnings: number
  pendingReferrals: number
  conversionRate: number
  averageCommission: number
}

interface ReferralTier {
  name: string
  requirements: number
  commission: number
  rewards: string[]
  current: boolean
  achieved: boolean
}

export default function ReferralPage() {
  const [referrals, setReferrals] = useState<Referral[]>([])
  const [stats, setStats] = useState<ReferralStats>({
    totalReferrals: 0,
    activeReferrals: 0,
    totalEarnings: 0,
    thisMonthEarnings: 0,
    pendingReferrals: 0,
    conversionRate: 0,
    averageCommission: 0
  })
  const [referralLink, setReferralLink] = useState("https://fundedhorizon.com/ref/USER123")
  const [isLoading, setIsLoading] = useState(true)
  const [selectedFilter, setSelectedFilter] = useState<string>("all")

  const referralTiers: ReferralTier[] = [
    {
      name: "Bronze",
      requirements: 0,
      commission: 5,
      rewards: ["5% commission", "Basic rewards"],
      current: true,
      achieved: true
    },
    {
      name: "Silver",
      requirements: 5,
      commission: 7,
      rewards: ["7% commission", "Priority support", "Exclusive content"],
      current: false,
      achieved: true
    },
    {
      name: "Gold",
      requirements: 15,
      commission: 10,
      rewards: ["10% commission", "VIP support", "Early access", "Custom rewards"],
      current: false,
      achieved: false
    },
    {
      name: "Platinum",
      requirements: 30,
      commission: 15,
      rewards: ["15% commission", "Dedicated manager", "Exclusive events", "Premium rewards"],
      current: false,
      achieved: false
    }
  ]

  useEffect(() => {
    fetchReferralData()
  }, [])

  const fetchReferralData = async () => {
    try {
      setIsLoading(true)
      // Mock data - replace with actual API call
      const mockReferrals: Referral[] = [
        {
          id: "1",
          name: "John Smith",
          email: "<EMAIL>",
          status: "active",
          joinDate: "2024-01-15",
          lastActivity: "2024-03-20",
          totalEarnings: 1250,
          commission: 62.50,
          level: "silver",
          avatar: ""
        },
        {
          id: "2",
          name: "Sarah Johnson",
          email: "<EMAIL>",
          status: "active",
          joinDate: "2024-02-10",
          lastActivity: "2024-03-19",
          totalEarnings: 890,
          commission: 44.50,
          level: "bronze",
          avatar: ""
        },
        {
          id: "3",
          name: "Mike Wilson",
          email: "<EMAIL>",
          status: "pending",
          joinDate: "2024-03-15",
          lastActivity: "2024-03-18",
          totalEarnings: 0,
          commission: 0,
          level: "bronze",
          avatar: ""
        },
        {
          id: "4",
          name: "Emily Davis",
          email: "<EMAIL>",
          status: "completed",
          joinDate: "2024-01-20",
          lastActivity: "2024-03-15",
          totalEarnings: 2100,
          commission: 105,
          level: "gold",
          avatar: ""
        }
      ]

      setReferrals(mockReferrals)
      
      // Calculate stats
      const totalReferrals = mockReferrals.length
      const activeReferrals = mockReferrals.filter(r => r.status === 'active').length
      const totalEarnings = mockReferrals.reduce((sum, r) => sum + r.totalEarnings, 0)
      const thisMonthEarnings = mockReferrals.reduce((sum, r) => sum + r.commission, 0)
      const pendingReferrals = mockReferrals.filter(r => r.status === 'pending').length
      const conversionRate = (activeReferrals / totalReferrals) * 100
      const averageCommission = totalEarnings / totalReferrals

      setStats({
        totalReferrals,
        activeReferrals,
        totalEarnings,
        thisMonthEarnings,
        pendingReferrals,
        conversionRate,
        averageCommission
      })
    } catch (error) {
      console.error('Error fetching referral data:', error)
      toast.error('Failed to load referral data')
    } finally {
      setIsLoading(false)
    }
  }

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink)
    toast.success('Referral link copied to clipboard!')
  }

  const shareReferralLink = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Join Funded Horizon',
        text: 'Start your trading journey with Funded Horizon!',
        url: referralLink
      })
    } else {
      copyReferralLink()
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      active: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: CheckCircle },
      pending: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: Clock },
      completed: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: Award },
      inactive: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: Users }
    }

    const badgeConfig = config[status as keyof typeof config]
    const Icon = badgeConfig.icon

    return (
      <Badge className={`${badgeConfig.color} border`}>
        <Icon size={12} className="mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getLevelBadge = (level: string) => {
    const config = {
      bronze: { color: "bg-amber-500/15 text-amber-400 border-amber-500/25", label: "Bronze" },
      silver: { color: "bg-gray-500/15 text-gray-400 border-gray-500/25", label: "Silver" },
      gold: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", label: "Gold" },
      platinum: { color: "bg-purple-500/15 text-purple-400 border-purple-500/25", label: "Platinum" }
    }

    const badgeConfig = config[level as keyof typeof config]

    return (
      <Badge className={`${badgeConfig.color} border`}>
        {badgeConfig.label}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const filteredReferrals = selectedFilter === "all" 
    ? referrals 
    : referrals.filter(ref => ref.status === selectedFilter)

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading referral data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Referral Program</h1>
          <p className="text-slate-400 mt-1">Earn rewards by inviting friends to join Funded Horizon</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25">
            <Users size={14} className="mr-1" />
            {stats.totalReferrals} Referrals
          </Badge>
        </div>
      </motion.div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Total Referrals</p>
                  <p className="text-2xl font-bold text-slate-100">{stats.totalReferrals}</p>
                  <p className="text-xs text-slate-500 mt-1">All time</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                  <Users size={24} className="text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Total Earnings</p>
                  <p className="text-2xl font-bold text-orange-400">{formatCurrency(stats.totalEarnings)}</p>
                  <p className="text-xs text-slate-500 mt-1">All time</p>
                </div>
                <div className="w-12 h-12 bg-orange-500/15 rounded-lg flex items-center justify-center">
                  <DollarSign size={24} className="text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Conversion Rate</p>
                  <p className="text-2xl font-bold text-green-400">{stats.conversionRate.toFixed(1)}%</p>
                  <p className="text-xs text-slate-500 mt-1">Active referrals</p>
                </div>
                <div className="w-12 h-12 bg-green-500/15 rounded-lg flex items-center justify-center">
                  <TrendingUp size={24} className="text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">This Month</p>
                  <p className="text-2xl font-bold text-blue-400">{formatCurrency(stats.thisMonthEarnings)}</p>
                  <p className="text-xs text-slate-500 mt-1">March 2024</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                  <Calendar size={24} className="text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Referral Link Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card className="bg-gradient-to-r from-blue-500/10 to-orange-500/10 border-blue-500/20">
          <CardHeader>
            <CardTitle className="text-slate-100 flex items-center gap-2">
              <Share2 size={20} />
              Your Referral Link
            </CardTitle>
            <CardDescription className="text-slate-400">
              Share this link with friends to earn rewards when they join
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-3">
              <div className="flex-1">
                <Input
                  value={referralLink}
                  readOnly
                  className="bg-slate-700/50 border-slate-600 text-slate-100 font-mono text-sm"
                />
              </div>
              <Button
                onClick={copyReferralLink}
                variant="outline"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <Copy size={16} className="mr-2" />
                Copy
              </Button>
              <Button
                onClick={shareReferralLink}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Share2 size={16} className="mr-2" />
                Share
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Referral Tiers */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="lg:col-span-1"
        >
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Award size={20} />
                Referral Tiers
              </CardTitle>
              <CardDescription className="text-slate-400">
                Unlock higher commissions and rewards
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {referralTiers.map((tier, index) => (
                <div
                  key={tier.name}
                  className={`p-4 rounded-lg border transition-all duration-300 ${
                    tier.current
                      ? 'bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-500/30'
                      : tier.achieved
                      ? 'bg-gradient-to-r from-blue-500/10 to-blue-600/10 border-blue-500/30'
                      : 'bg-slate-700/30 border-slate-600/50'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`font-semibold ${
                      tier.current ? 'text-orange-400' : tier.achieved ? 'text-blue-400' : 'text-slate-400'
                    }`}>
                      {tier.name}
                    </h4>
                    <div className="flex items-center gap-2">
                      {tier.current && <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25">Current</Badge>}
                      {tier.achieved && !tier.current && <CheckCircle size={16} className="text-blue-400" />}
                    </div>
                  </div>
                  <p className="text-sm text-slate-300 mb-2">
                    {tier.requirements} referrals • {tier.commission}% commission
                  </p>
                  <ul className="space-y-1">
                    {tier.rewards.map((reward, idx) => (
                      <li key={idx} className="text-xs text-slate-400 flex items-center gap-2">
                        <Star size={12} className="text-orange-400" />
                        {reward}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Referrals List */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.7 }}
          className="lg:col-span-2"
        >
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-slate-100">Your Referrals</CardTitle>
                  <CardDescription className="text-slate-400">
                    Track your referral network and earnings
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={selectedFilter === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedFilter("all")}
                    className={selectedFilter === "all" ? "bg-blue-600" : "border-slate-600 text-slate-300"}
                  >
                    All
                  </Button>
                  <Button
                    variant={selectedFilter === "active" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedFilter("active")}
                    className={selectedFilter === "active" ? "bg-green-600" : "border-slate-600 text-slate-300"}
                  >
                    Active
                  </Button>
                  <Button
                    variant={selectedFilter === "pending" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedFilter("pending")}
                    className={selectedFilter === "pending" ? "bg-yellow-600" : "border-slate-600 text-slate-300"}
                  >
                    Pending
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-slate-700">
                      <TableHead className="text-slate-300">User</TableHead>
                      <TableHead className="text-slate-300">Status</TableHead>
                      <TableHead className="text-slate-300">Level</TableHead>
                      <TableHead className="text-slate-300">Join Date</TableHead>
                      <TableHead className="text-slate-300">Earnings</TableHead>
                      <TableHead className="text-slate-300">Commission</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredReferrals.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8 text-slate-400">
                          No referrals found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredReferrals.map((referral) => (
                        <TableRow key={referral.id} className="border-slate-700 hover:bg-slate-700/30">
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-orange-500 rounded-full flex items-center justify-center">
                                <Users size={16} className="text-white" />
                              </div>
                              <div>
                                <p className="text-slate-200 font-medium text-sm">{referral.name}</p>
                                <p className="text-slate-400 text-xs">{referral.email}</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{getStatusBadge(referral.status)}</TableCell>
                          <TableCell>{getLevelBadge(referral.level)}</TableCell>
                          <TableCell className="text-slate-300 text-sm">
                            {formatDate(referral.joinDate)}
                          </TableCell>
                          <TableCell className="text-slate-200 font-medium">
                            {formatCurrency(referral.totalEarnings)}
                          </TableCell>
                          <TableCell className="text-orange-400 font-medium">
                            {formatCurrency(referral.commission)}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}

