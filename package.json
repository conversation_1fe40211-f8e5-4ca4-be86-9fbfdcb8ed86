{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@chakra-ui/react": "^3.8.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.1", "@mui/material": "^6.4.5", "@nivo/core": "^0.88.0", "@nivo/sankey": "^0.88.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-three/drei": "^9.121.3", "@react-three/fiber": "^8.2.2", "@tremor/react": "^3.18.7", "autoprefixer": "^10.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "countries-list": "^3.1.1", "critters": "^0.0.23", "date-fns": "^3.6.0", "disable-devtool": "^0.3.8", "embla-carousel-react": "8.5.1", "framer-motion": "^11.18.2", "framer-motion-3d": "^12.0.5", "input-otp": "1.4.1", "lightweight-charts": "^5.0.2", "lucide-react": "^0.454.0", "next": "^14.2.17", "next-themes": "^0.4.4", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-flags-select": "^2.2.3", "react-hook-form": "^7.54.1", "react-native-webview": "^13.13.5", "react-phone-input-2": "^2.15.1", "react-resizable-panels": "^2.1.7", "react-toastify": "^11.0.3", "react-tradingview-widget": "^1.3.2", "recharts": "2.15.0", "shadcn-ui": "^0.9.4", "sonner": "^1.7.1", "swiper": "^11.2.6", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.172.0", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.4.14", "tailwindcss": "^3.3.0", "typescript": "^5"}}