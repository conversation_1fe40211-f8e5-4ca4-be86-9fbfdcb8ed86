import { getBaseUrl } from './env';

// API Configuration
export const API_CONFIG = {
  endpoints: {
    login: 'auth/login',
    signup: 'auth/signup',
    order: 'order',
    orderIds: 'order/order_ids',
    accountDetails: 'order/account_detail',
    failOrder: 'order/fail_order',
    failedOrders: 'order/failed_orders',
    myfxbookFetch: 'myfxbook/fetch_account_details',
    // Admin endpoints
    runningOrders: 'admin/running_orders',
    completedOrders: 'admin/completed_orders',
    orders: 'admin/orders',
    passedOrders: 'admin/passed_orders',
    stageTwoOrders: 'admin/stage_two_orders',
    liveOrders: 'admin/live_orders',
    orderStats: 'admin/order_stats',
    userStats: 'admin/user_stats',
    users: 'admin/users'
  }
} as const;

// Helper function to get full API URL
export const getApiUrl = (endpoint: keyof typeof API_CONFIG.endpoints | string): string => {
  const baseUrl = getBaseUrl();
  if (typeof endpoint === 'string') {
    // Remove leading slash if present in the endpoint
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${baseUrl}${cleanEndpoint}`;
  }
  return `${baseUrl}${API_CONFIG.endpoints[endpoint as keyof typeof API_CONFIG.endpoints]}`;
};

// Secure fetch functions
export const secureFetch = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('access_token');

  // Don't set Content-Type for FormData - let the browser set it with boundary
  const isFormData = options.body instanceof FormData;

  const headers: Record<string, string> = {
    ...options.headers as Record<string, string>,
    ...(token && { 'Authorization': `Bearer ${token}` })
  };

  // Only set Content-Type for non-FormData requests
  if (!isFormData) {
    headers['Content-Type'] = 'application/json';
  }

  const apiEndpoint = getApiUrl(endpoint);

  const response = await fetch(apiEndpoint, {
    ...options,
    headers
  });

  if (!response.ok) {
    console.error(`API request failed: ${response.status} ${response.statusText}`);
    throw new Error(`API request failed: ${response.status}`);
  }

  return response;
};

// Direct backend fetch (for server-side only)
export const backendFetch = async (path: string, options: RequestInit = {}) => {
  const baseUrl = getBaseUrl();
  const response = await fetch(`${baseUrl}${path}`, options);

  if (!response.ok) {
    throw new Error('Backend request failed');
  }

  return response;
}; 