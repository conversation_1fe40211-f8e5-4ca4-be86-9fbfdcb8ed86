"use client"

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { TrendingUp, Shield, Clock, ArrowRight, Zap, Sparkles, Crown, Star } from 'lucide-react'

const ACCOUNT_SIZES = [
  "$1,000", "$3,000", "$5,000", "$10,000", "$25,000",
  "$50,000", "$100,000", "$200,000", "$500,000"
] as const

type AccountSize = typeof ACCOUNT_SIZES[number]

interface ChallengeRules {
  "Profit Target": string
  "Profit Second target"?: string
  "Daily Drawdown": string
  "Max Drawdown": string
  "Profit Split": string
  "Min Trading Days": string
  "Leverage": string
}

interface ChallengeType {
  color: string
  icon: React.ElementType
  description: string
  rules: ChallengeRules
  prices: Record<AccountSize, string>
  features: string[]
  popularityScore: number
  salePrice?: Record<AccountSize, string>
  badge?: string
  savings: Record<AccountSize, string>
  isPopular?: Record<AccountSize, boolean>
  discountPercentage: Record<AccountSize, string>
}

const CHALLENGE_TYPES: Record<string, ChallengeType> = {
  'HFT NEO': {
    color: "from-purple-600 to-purple-800",
    icon: Crown,
    description: "For elite traders seeking maximum performance",
    rules: {
      "Profit Target": "8%",
      "Daily Drawdown": "5%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 90%",
      "Min Trading Days": "0",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$18.75",
      "$3,000": "$33.75",
      "$5,000": "$46.25",
      "$10,000": "$78.75",
      "$25,000": "$172.50",
      "$50,000": "$316.67",
      "$100,000": "$550.00",
      "$200,000": "$896.67",
      "$500,000": "$1,616.67"
    },
    isPopular: {
      "$1,000": true,
      "$3,000": false,
      "$5,000": false,
      "$10,000": true,
      "$25,000": false,
      "$50,000": true,
      "$100,000": true,
      "$200,000": false,
      "$500,000": true
    },
    salePrice: {
      "$1,000": "$15",
      "$3,000": "$27",
      "$5,000": "$37",
      "$10,000": "$63",
      "$25,000": "$138",
      "$50,000": "$95",
      "$100,000": "$165",
      "$200,000": "$269",
      "$500,000": "$485"
    },
    discountPercentage: {
      "$1,000": "20% OFF",
      "$3,000": "20% OFF",
      "$5,000": "20% OFF",
      "$10,000": "20% OFF",
      "$25,000": "20% OFF",
      "$50,000": "70% OFF",
      "$100,000": "70% OFF",
      "$200,000": "70% OFF",
      "$500,000": "70% OFF"
    },
    features: [
      "Ultra-fast execution speeds",
      "Advanced trading tools access",
      "Priority support 24/7",
      "Custom leverage options",
      "Real-time performance analytics"
    ],
    popularityScore: 98,
    badge: "Limited Time Offer",
    savings: {
      "$1,000": "Save $3.75",
      "$3,000": "Save $6.75",
      "$5,000": "Save $9.25",
      "$10,000": "Save $15.75",
      "$25,000": "Save $34.50",
      "$50,000": "Save $221.67",
      "$100,000": "Save $385.00",
      "$200,000": "Save $627.67",
      "$500,000": "Save $1,131.67"
    }
  },
  'One-Step': {
    color: "from-orange-500 to-orange-700",
    icon: Zap,
    description: "Advanced challenge for consistent traders",
    rules: {
      "Profit Target": "10%",
      "Daily Drawdown": "4%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 80%",
      "Min Trading Days": "5",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$10.00",
      "$3,000": "$18.75",
      "$5,000": "$30.00",
      "$10,000": "$52.50",
      "$25,000": "$107.50",
      "$50,000": "$183.33",
      "$100,000": "$296.67",
      "$200,000": "$496.67",
      "$500,000": "$963.33"
    },
    isPopular: {
      "$1,000": true,
      "$3,000": true,
      "$5,000": false,
      "$10,000": true,
      "$25,000": true,
      "$50,000": false,
      "$100,000": false,
      "$200,000": true,
      "$500,000": false
    },
    salePrice: {
      "$1,000": "$8",
      "$3,000": "$15",
      "$5,000": "$24",
      "$10,000": "$42",
      "$25,000": "$86",
      "$50,000": "$55",
      "$100,000": "$89",
      "$200,000": "$149",
      "$500,000": "$289"
    },
    discountPercentage: {
      "$1,000": "20% OFF",
      "$3,000": "20% OFF",
      "$5,000": "20% OFF",
      "$10,000": "20% OFF",
      "$25,000": "20% OFF",
      "$50,000": "70% OFF",
      "$100,000": "70% OFF",
      "$200,000": "70% OFF",
      "$500,000": "70% OFF"
    },
    features: [
      "Intermediate trading conditions",
      "Standard support package",
      "Basic performance metrics",
      "Regular market updates",
      "Trading community access"
    ],
    popularityScore: 92,
    badge: "Limited Time Offer",
    savings: {
      "$1,000": "Save $2.00",
      "$3,000": "Save $3.75",
      "$5,000": "Save $6.00",
      "$10,000": "Save $10.50",
      "$25,000": "Save $21.50",
      "$50,000": "Save $128.33",
      "$100,000": "Save $207.67",
      "$200,000": "Save $347.67",
      "$500,000": "Save $674.33"
    }
  },
  'Two-Step': {
    color: "from-blue-600 to-blue-800",
    icon: Shield,
    description: "Perfect starting point for new traders",
    rules: {
      "Profit Target": "10%",
      "Profit Second target": "5%",
      "Daily Drawdown": "4%",
      "Max Drawdown": "10%",
      "Profit Split": "Up to 80%",
      "Min Trading Days": "5",
      "Leverage": "1:100"
    },
    prices: {
      "$1,000": "$7.50",
      "$3,000": "$15.00",
      "$5,000": "$26.25",
      "$10,000": "$41.25",
      "$25,000": "$90.00",
      "$50,000": "$140.00",
      "$100,000": "$216.67",
      "$200,000": "$426.67",
      "$500,000": "$750.00"
    },
    isPopular: {
      "$1,000": true,
      "$3,000": true,
      "$5,000": true,
      "$10,000": false,
      "$25,000": true,
      "$50,000": false,
      "$100,000": false,
      "$200,000": false,
      "$500,000": false
    },
    salePrice: {
      "$1,000": "$6",
      "$3,000": "$12",
      "$5,000": "$21",
      "$10,000": "$33",
      "$25,000": "$72",
      "$50,000": "$42",
      "$100,000": "$65",
      "$200,000": "$128",
      "$500,000": "$225"
    },
    discountPercentage: {
      "$1,000": "20% OFF",
      "$3,000": "20% OFF",
      "$5,000": "20% OFF",
      "$10,000": "20% OFF",
      "$25,000": "20% OFF",
      "$50,000": "70% OFF",
      "$100,000": "70% OFF",
      "$200,000": "70% OFF",
      "$500,000": "70% OFF"
    },
    features: [
      "Beginner-friendly rules",
      "Educational resources",
      "Basic support package",
      "Standard market tools",
      "Weekly market insights"
    ],
    popularityScore: 85,
    badge: "Limited Time Offer",
    savings: {
      "$1,000": "Save $1.50",
      "$3,000": "Save $3.00",
      "$5,000": "Save $5.25",
      "$10,000": "Save $8.25",
      "$25,000": "Save $18.00",
      "$50,000": "Save $98.00",
      "$100,000": "Save $151.67",
      "$200,000": "Save $298.67",
      "$500,000": "Save $525.00"
    }
  }
}

export default function TradingChallenge({ isDashboard = false }) {
  const [selectedBalance, setSelectedBalance] = useState<AccountSize>("$10,000")
  const [selectedAccounts, setSelectedAccounts] = useState<AccountSize[]>([])

  const toggleAccount = (size: AccountSize) => {
    if (selectedAccounts.includes(size)) {
      setSelectedAccounts(selectedAccounts.filter(s => s !== size))
    } else {
      setSelectedAccounts([...selectedAccounts, size])
    }
  }

  return (
    <section className="relative min-h-screen py-20 bg-[#0A0F1C]">
      <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 to-transparent" />

      <div className="container mx-auto px-4 relative">
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold bg-gradient-to-r from-orange-400 to-blue-400 bg-clip-text text-transparent mb-6">
            Choose Your Challenge
          </h2>
          <p className="text-xl text-blue-200/80 max-w-3xl mx-auto">
            Select from our range of trading challenges designed to match your experience level
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {ACCOUNT_SIZES.map((size) => (
            <button
              key={size}
              type="button"
              onClick={() => setSelectedBalance(size)}
              className={`px-4 py-2 rounded-xl transition-all duration-300 ${
                selectedBalance === size
                  ? "bg-gradient-to-r from-orange-500 to-blue-600 text-white shadow-md"
                  : "bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white"
              }`}
            >
              {size}
            </button>
          ))}
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          {Object.entries(CHALLENGE_TYPES).map(([name, data]) => (
            <div
              key={name}
              className="relative bg-zinc-950 rounded-xl overflow-hidden group shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
            >
              {/* Card header with icon */}
              <div className={`p-4 pt-5 flex items-center gap-3 bg-gradient-to-r ${data.color}`}>
                <div className="p-2.5 rounded-lg bg-white/10">
                  <data.icon className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">{name}</h3>
                  <p className="text-xs text-gray-300">{data.description}</p>
                </div>
              </div>

              {/* Badges */}
              {data.isPopular && data.isPopular[selectedBalance] && (
                <div className="absolute top-1 left-1 p-1 bg-gradient-to-r from-yellow-300 to-amber-500 rounded-full shadow-md z-20 flex items-center justify-center transform hover:scale-110 transition-transform duration-300">
                  <Crown className="w-4 h-4 text-amber-900" />
                </div>
              )}

              {data.badge && (
                <div className="absolute top-0 right-4 px-3 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white text-xs font-bold rounded-b-md shadow-md z-10 flex items-center gap-1.5">
                  <Crown className="w-3.5 h-3.5 text-white" />
                  <span>Limited Time Offer</span>
                </div>
              )}

              {/* Card content */}
              <div className="p-4">
                {/* Price section */}
                <div className="mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-400 font-medium">Price</span>
                    <div className="flex flex-col items-end">
                      <div className="flex items-center gap-2">
                        <span className="text-base text-gray-500 line-through">
                          {data.prices[selectedBalance]}
                        </span>
                        <span className="text-xs font-bold text-orange-500 bg-orange-500/10 px-2 py-0.5 rounded">
                          {data.discountPercentage[selectedBalance]}
                        </span>
                      </div>
                      {data.salePrice && (
                        <div className="flex items-center gap-2">
                          <span className="text-2xl font-bold text-green-400">
                            {data.salePrice[selectedBalance]}
                          </span>
                          <span className="text-xs font-medium text-green-300">
                            {data.savings[selectedBalance]}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Progress bar */}
                  <div className="h-1.5 bg-gray-800 rounded-full mt-2 overflow-hidden">
                    <motion.div
                      className={`h-full bg-gradient-to-r ${data.color}`}
                      initial={{ width: 0 }}
                      animate={{ width: `${data.popularityScore}%` }}
                      transition={{ duration: 1, ease: "easeOut" }}
                    />
                  </div>
                </div>

                {/* Rules section */}
                <div className="space-y-0 mb-4">
                  {Object.entries(data.rules).map(([rule, value]) => (
                    <div
                      key={rule}
                      className="flex items-center justify-between text-sm py-2 border-b border-gray-800/50"
                    >
                      <span className="text-gray-400">{rule}</span>
                      <span className="text-white font-semibold">{value}</span>
                    </div>
                  ))}
                </div>

                {/* Button */}
                <button
                  type="button"
                  onClick={() => window.location.href = isDashboard ? '/dashboard/buy' : '/signup'}
                  className={`w-full bg-gradient-to-r ${data.color} text-white py-3.5 rounded-md font-bold text-sm flex items-center justify-center gap-2 transition-all duration-300 hover:brightness-110 hover:scale-[1.02]`}
                >
                  Get Started
                  <ArrowRight className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
