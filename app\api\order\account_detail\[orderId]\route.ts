import { headers } from 'next/headers';
import { getBaseUrl } from '@/app/config/env';
import { createSecureResponse } from '@/app/lib/api-helpers';

export async function GET(
  request: Request,
  { params }: { params: { orderId: string } }
) {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return createSecureResponse({
        message: 'Unauthorized',
        status: 401
      });
    }

    const { orderId } = params;

    if (!orderId) {
      return createSecureResponse({
        message: 'Order ID is required',
        status: 400
      });
    }

    console.log(`Fetching account details for order ID: ${orderId}`);

    const response = await fetch(`${getBaseUrl()}/order/account_detail/${orderId}`, {
      headers: {
        'Authorization': authHeader,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`Backend request failed with status: ${response.status}`);
      return createSecureResponse({
        message: `Failed to fetch account details (Status: ${response.status})`,
        status: response.status
      });
    }

    const data = await response.json();
    console.log('Account details response:', data);

    return createSecureResponse({
      data,
      status: 200
    });
  } catch (error) {
    console.error('Error in account details API:', error);
    return createSecureResponse({
      message: 'Internal server error',
      status: 500
    });
  }
}
