"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Calendar,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3,
  DollarSign,
  Percent
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { secureFetch } from "@/app/config/api"

interface Order {
  id: string
  symbol: string
  type: 'BUY' | 'SELL'
  status: 'OPEN' | 'CLOSED' | 'CANCELLED' | 'PENDING'
  openPrice: number
  closePrice?: number
  volume: number
  profit?: number
  openTime: string
  closeTime?: string
  account: string
  strategy?: string
  notes?: string
}

interface OrderStats {
  totalOrders: number
  openOrders: number
  closedOrders: number
  totalProfit: number
  winRate: number
  averageProfit: number
}

export default function OrderHistoryPage() {
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [stats, setStats] = useState<OrderStats>({
    totalOrders: 0,
    openOrders: 0,
    closedOrders: 0,
    totalProfit: 0,
    winRate: 0,
    averageProfit: 0
  })
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [symbolFilter, setSymbolFilter] = useState<string>("all")
  const [dateFilter, setDateFilter] = useState<string>("all")

  useEffect(() => {
    fetchOrders()
  }, [])

  useEffect(() => {
    filterOrders()
  }, [orders, searchTerm, statusFilter, symbolFilter, dateFilter])

  const fetchOrders = async () => {
    try {
      setIsLoading(true)
      const response = await secureFetch('/order/order_ids')
      
      if (response.ok) {
        const data = await response.json()
        // Transform the data to match our Order interface
        const transformedOrders = data.data || data || []
        setOrders(transformedOrders)
        calculateStats(transformedOrders)
      } else {
        console.error('Failed to fetch orders')
      }
    } catch (error) {
      console.error('Error fetching orders:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const calculateStats = (orderList: Order[]) => {
    const closedOrders = orderList.filter(order => order.status === 'CLOSED')
    const openOrders = orderList.filter(order => order.status === 'OPEN')
    const totalProfit = closedOrders.reduce((sum, order) => sum + (order.profit || 0), 0)
    const winningOrders = closedOrders.filter(order => (order.profit || 0) > 0)
    
    setStats({
      totalOrders: orderList.length,
      openOrders: openOrders.length,
      closedOrders: closedOrders.length,
      totalProfit,
      winRate: closedOrders.length > 0 ? (winningOrders.length / closedOrders.length) * 100 : 0,
      averageProfit: closedOrders.length > 0 ? totalProfit / closedOrders.length : 0
    })
  }

  const filterOrders = () => {
    let filtered = orders

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.id.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Symbol filter
    if (symbolFilter !== "all") {
      filtered = filtered.filter(order => order.symbol === symbolFilter)
    }

    // Date filter
    if (dateFilter !== "all") {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case "today":
          filterDate.setHours(0, 0, 0, 0)
          filtered = filtered.filter(order => new Date(order.openTime) >= filterDate)
          break
        case "week":
          filterDate.setDate(filterDate.getDate() - 7)
          filtered = filtered.filter(order => new Date(order.openTime) >= filterDate)
          break
        case "month":
          filterDate.setMonth(filterDate.getMonth() - 1)
          filtered = filtered.filter(order => new Date(order.openTime) >= filterDate)
          break
      }
    }

    setFilteredOrders(filtered)
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      OPEN: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: Clock },
      CLOSED: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: CheckCircle },
      CANCELLED: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: XCircle },
      PENDING: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: AlertCircle }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.PENDING
    const Icon = config.icon

    return (
      <Badge className={`${config.color} border`}>
        <Icon size={12} className="mr-1" />
        {status}
      </Badge>
    )
  }

  const getTypeBadge = (type: string) => {
    const isBuy = type === 'BUY'
    return (
      <Badge className={`${isBuy ? 'bg-green-500/15 text-green-400 border-green-500/25' : 'bg-red-500/15 text-red-400 border-red-500/25'} border`}>
        {isBuy ? <TrendingUp size={12} className="mr-1" /> : <TrendingDown size={12} className="mr-1" />}
        {type}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const uniqueSymbols = [...new Set(orders.map(order => order.symbol))]

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading order history...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Order History</h1>
          <p className="text-slate-400 mt-1">Track and analyze your trading performance</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Download size={16} className="mr-2" />
          Export Data
        </Button>
      </motion.div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Total Orders</p>
                <p className="text-2xl font-bold text-slate-100">{stats.totalOrders}</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                <BarChart3 size={24} className="text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Open Orders</p>
                <p className="text-2xl font-bold text-slate-100">{stats.openOrders}</p>
              </div>
              <div className="w-12 h-12 bg-yellow-500/15 rounded-lg flex items-center justify-center">
                <Clock size={24} className="text-yellow-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Total Profit</p>
                <p className={`text-2xl font-bold ${stats.totalProfit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatCurrency(stats.totalProfit)}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-500/15 rounded-lg flex items-center justify-center">
                <DollarSign size={24} className="text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Win Rate</p>
                <p className="text-2xl font-bold text-slate-100">{stats.winRate.toFixed(1)}%</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/15 rounded-lg flex items-center justify-center">
                <Percent size={24} className="text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-100 flex items-center gap-2">
            <Filter size={20} />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" size={16} />
              <Input
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700/50 border-slate-600 text-slate-100"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="OPEN">Open</SelectItem>
                <SelectItem value="CLOSED">Closed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
              </SelectContent>
            </Select>

            <Select value={symbolFilter} onValueChange={setSymbolFilter}>
              <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                <SelectValue placeholder="Symbol" />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600">
                <SelectItem value="all">All Symbols</SelectItem>
                {uniqueSymbols.map(symbol => (
                  <SelectItem key={symbol} value={symbol}>{symbol}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                <SelectValue placeholder="Date Range" />
              </SelectTrigger>
              <SelectContent className="bg-slate-700 border-slate-600">
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Last 7 Days</SelectItem>
                <SelectItem value="month">Last 30 Days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-100">Orders ({filteredOrders.length})</CardTitle>
          <CardDescription className="text-slate-400">
            Detailed view of all your trading orders
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="border-slate-700">
                  <TableHead className="text-slate-300">Order ID</TableHead>
                  <TableHead className="text-slate-300">Symbol</TableHead>
                  <TableHead className="text-slate-300">Type</TableHead>
                  <TableHead className="text-slate-300">Status</TableHead>
                  <TableHead className="text-slate-300">Volume</TableHead>
                  <TableHead className="text-slate-300">Open Price</TableHead>
                  <TableHead className="text-slate-300">Close Price</TableHead>
                  <TableHead className="text-slate-300">Profit/Loss</TableHead>
                  <TableHead className="text-slate-300">Open Time</TableHead>
                  <TableHead className="text-slate-300">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={10} className="text-center py-8 text-slate-400">
                      No orders found matching your criteria
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredOrders.map((order) => (
                    <TableRow key={order.id} className="border-slate-700 hover:bg-slate-700/30">
                      <TableCell className="text-slate-200 font-mono text-sm">{order.id}</TableCell>
                      <TableCell className="text-slate-200 font-semibold">{order.symbol}</TableCell>
                      <TableCell>{getTypeBadge(order.type)}</TableCell>
                      <TableCell>{getStatusBadge(order.status)}</TableCell>
                      <TableCell className="text-slate-200">{order.volume}</TableCell>
                      <TableCell className="text-slate-200">{formatCurrency(order.openPrice)}</TableCell>
                      <TableCell className="text-slate-200">
                        {order.closePrice ? formatCurrency(order.closePrice) : '-'}
                      </TableCell>
                      <TableCell className={order.profit && order.profit >= 0 ? 'text-green-400' : 'text-red-400'}>
                        {order.profit ? formatCurrency(order.profit) : '-'}
                      </TableCell>
                      <TableCell className="text-slate-400 text-sm">
                        {formatDate(order.openTime)}
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm" className="text-slate-400 hover:text-slate-200">
                          <Eye size={16} />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 