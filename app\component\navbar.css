/* Custom navbar styles */

/* Smooth transition for navbar background */
.navbar-transparent {
  background-color: transparent;
  -webkit-backdrop-filter: none;
  backdrop-filter: none;
  box-shadow: none;
}

.navbar-scrolled {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Dynamic background opacity classes */
.navbar-opacity-10 {
  background-color: rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}
.navbar-opacity-20 {
  background-color: rgba(0, 0, 0, 0.2);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}
.navbar-opacity-30 {
  background-color: rgba(0, 0, 0, 0.3);
  -webkit-backdrop-filter: blur(6px);
  backdrop-filter: blur(6px);
}
.navbar-opacity-40 {
  background-color: rgba(0, 0, 0, 0.4);
  -webkit-backdrop-filter: blur(7px);
  backdrop-filter: blur(7px);
}
.navbar-opacity-50 {
  background-color: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}
.navbar-opacity-60 {
  background-color: rgba(0, 0, 0, 0.6);
  -webkit-backdrop-filter: blur(9px);
  backdrop-filter: blur(9px);
}
.navbar-opacity-70 {
  background-color: rgba(0, 0, 0, 0.7);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}
.navbar-opacity-80 {
  background-color: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
}

/* Hover effect for nav items */
.nav-item {
  position: relative;
}

.nav-item::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(to right, #f97316, #2563eb);
  transition: width 0.3s ease;
}

.nav-item:hover::after {
  width: 100%;
}

/* Active nav item */
.nav-item-active::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -4px;
  left: 0;
  background: linear-gradient(to right, #f97316, #2563eb);
}

/* Logo hover effect */
.logo-container {
  transition: transform 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
}

/* Mobile menu button hover effect */
.mobile-menu-button {
  transition: all 0.3s ease;
}

.mobile-menu-button:hover {
  transform: rotate(90deg);
}

/* Media queries for responsive adjustments */
@media (max-width: 640px) {
  .navbar {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
}
