"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Coins, 
  TrendingUp, 
  Gift, 
  Clock, 
  CheckCircle,
  Star,
  Target,
  Zap,
  Calendar,
  DollarSign,
  ArrowRight,
  Play,
  Award,
  Users,
  BarChart3,
  RefreshCw
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { toast } from "react-toastify"

interface CoinTask {
  id: string
  title: string
  description: string
  coins: number
  type: 'daily' | 'weekly' | 'one-time' | 'achievement'
  status: 'available' | 'completed' | 'locked'
  progress: number
  maxProgress: number
  icon: string
  category: 'trading' | 'social' | 'learning' | 'referral'
}

interface CoinBalance {
  total: number
  earned: number
  spent: number
  available: number
  pending: number
}

interface CoinReward {
  id: string
  name: string
  description: string
  coins: number
  type: 'discount' | 'bonus' | 'feature' | 'physical'
  status: 'available' | 'purchased' | 'locked'
  image: string
}

export default function EarnCoinPage() {
  const [coinBalance, setCoinBalance] = useState<CoinBalance>({
    total: 1250,
    earned: 1500,
    spent: 250,
    available: 1000,
    pending: 0
  })
  const [tasks, setTasks] = useState<CoinTask[]>([])
  const [rewards, setRewards] = useState<CoinReward[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchCoinData()
  }, [])

  const fetchCoinData = async () => {
    try {
      setIsLoading(true)
      // Mock data - replace with actual API call
      const mockTasks: CoinTask[] = [
        {
          id: "1",
          title: "Complete Daily Login",
          description: "Log in to your account today",
          coins: 10,
          type: "daily",
          status: "completed",
          progress: 1,
          maxProgress: 1,
          icon: "🎯",
          category: "trading"
        },
        {
          id: "2",
          title: "Make 5 Trades",
          description: "Complete 5 successful trades",
          coins: 50,
          type: "daily",
          status: "available",
          progress: 3,
          maxProgress: 5,
          icon: "📈",
          category: "trading"
        },
        {
          id: "3",
          title: "Watch Trading Tutorial",
          description: "Complete a trading education video",
          coins: 25,
          type: "one-time",
          status: "available",
          progress: 0,
          maxProgress: 1,
          icon: "🎓",
          category: "learning"
        },
        {
          id: "4",
          title: "Refer a Friend",
          description: "Invite a friend to join the platform",
          coins: 100,
          type: "one-time",
          status: "available",
          progress: 0,
          maxProgress: 1,
          icon: "👥",
          category: "referral"
        },
        {
          id: "5",
          title: "Share on Social Media",
          description: "Share your trading success story",
          coins: 30,
          type: "weekly",
          status: "available",
          progress: 0,
          maxProgress: 1,
          icon: "📱",
          category: "social"
        },
        {
          id: "6",
          title: "Achieve 70% Win Rate",
          description: "Maintain 70% win rate for a week",
          coins: 200,
          type: "achievement",
          status: "locked",
          progress: 65,
          maxProgress: 70,
          icon: "🏆",
          category: "trading"
        }
      ]

      const mockRewards: CoinReward[] = [
        {
          id: "1",
          name: "10% Trading Bonus",
          description: "Get 10% bonus on your next deposit",
          coins: 500,
          type: "bonus",
          status: "available",
          image: "💰"
        },
        {
          id: "2",
          name: "Premium Support",
          description: "Access to priority customer support",
          coins: 300,
          type: "feature",
          status: "available",
          image: "🎧"
        },
        {
          id: "3",
          name: "Trading Course",
          description: "Access to advanced trading course",
          coins: 750,
          type: "feature",
          status: "available",
          image: "📚"
        },
        {
          id: "4",
          name: "Funded Horizon T-Shirt",
          description: "Exclusive branded merchandise",
          coins: 1000,
          type: "physical",
          status: "available",
          image: "👕"
        }
      ]

      setTasks(mockTasks)
      setRewards(mockRewards)
    } catch (error) {
      console.error('Error fetching coin data:', error)
      toast.error('Failed to load coin data')
    } finally {
      setIsLoading(false)
    }
  }

  const completeTask = async (taskId: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setTasks(prev => prev.map(task => 
        task.id === taskId 
          ? { ...task, status: 'completed', progress: task.maxProgress }
          : task
      ))
      
      setCoinBalance(prev => ({
        ...prev,
        total: prev.total + tasks.find(t => t.id === taskId)?.coins || 0,
        earned: prev.earned + tasks.find(t => t.id === taskId)?.coins || 0,
        available: prev.available + tasks.find(t => t.id === taskId)?.coins || 0
      }))
      
      toast.success(`Task completed! Earned ${tasks.find(t => t.id === taskId)?.coins} coins`)
    } catch (error) {
      toast.error('Failed to complete task')
    }
  }

  const purchaseReward = async (rewardId: string) => {
    try {
      const reward = rewards.find(r => r.id === rewardId)
      if (!reward) return
      
      if (coinBalance.available < reward.coins) {
        toast.error('Insufficient coins')
        return
      }
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setRewards(prev => prev.map(r => 
        r.id === rewardId ? { ...r, status: 'purchased' } : r
      ))
      
      setCoinBalance(prev => ({
        ...prev,
        total: prev.total - reward.coins,
        spent: prev.spent + reward.coins,
        available: prev.available - reward.coins
      }))
      
      toast.success(`Reward purchased: ${reward.name}`)
    } catch (error) {
      toast.error('Failed to purchase reward')
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      available: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: CheckCircle },
      completed: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: Award },
      locked: { color: "bg-slate-500/15 text-slate-400 border-slate-500/25", icon: Clock },
      purchased: { color: "bg-orange-500/15 text-orange-400 border-orange-500/25", icon: Gift }
    }

    const badgeConfig = config[status as keyof typeof config]
    const Icon = badgeConfig.icon

    return (
      <Badge className={`${badgeConfig.color} border`}>
        <Icon size={12} className="mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getCategoryIcon = (category: string) => {
    const icons = {
      trading: "📈",
      social: "📱",
      learning: "🎓",
      referral: "👥"
    }
    return icons[category as keyof typeof icons] || "🎯"
  }

  const filteredTasks = selectedCategory === "all" 
    ? tasks 
    : tasks.filter(task => task.category === selectedCategory)

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading coin data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Earn Coins</h1>
          <p className="text-slate-400 mt-1">Complete tasks and earn rewards with your coins</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25">
            <Coins size={14} className="mr-1" />
            {coinBalance.total} Coins
          </Badge>
        </div>
      </motion.div>

      {/* Coin Balance Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Total Coins</p>
                  <p className="text-2xl font-bold text-orange-400">{coinBalance.total}</p>
                  <p className="text-xs text-slate-500 mt-1">All time</p>
                </div>
                <div className="w-12 h-12 bg-orange-500/15 rounded-lg flex items-center justify-center">
                  <Coins size={24} className="text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Available</p>
                  <p className="text-2xl font-bold text-green-400">{coinBalance.available}</p>
                  <p className="text-xs text-slate-500 mt-1">Ready to spend</p>
                </div>
                <div className="w-12 h-12 bg-green-500/15 rounded-lg flex items-center justify-center">
                  <DollarSign size={24} className="text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Earned</p>
                  <p className="text-2xl font-bold text-blue-400">{coinBalance.earned}</p>
                  <p className="text-xs text-slate-500 mt-1">Total earned</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                  <TrendingUp size={24} className="text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Spent</p>
                  <p className="text-2xl font-bold text-purple-400">{coinBalance.spent}</p>
                  <p className="text-xs text-slate-500 mt-1">On rewards</p>
                </div>
                <div className="w-12 h-12 bg-purple-500/15 rounded-lg flex items-center justify-center">
                  <Gift size={24} className="text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tasks Section */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-slate-100 flex items-center gap-2">
                    <Target size={20} />
                    Earn Coins
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    Complete tasks to earn coins
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={selectedCategory === "all" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory("all")}
                    className={selectedCategory === "all" ? "bg-orange-600" : "border-slate-600 text-slate-300"}
                  >
                    All
                  </Button>
                  <Button
                    variant={selectedCategory === "trading" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory("trading")}
                    className={selectedCategory === "trading" ? "bg-blue-600" : "border-slate-600 text-slate-300"}
                  >
                    Trading
                  </Button>
                  <Button
                    variant={selectedCategory === "social" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory("social")}
                    className={selectedCategory === "social" ? "bg-green-600" : "border-slate-600 text-slate-300"}
                  >
                    Social
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {filteredTasks.map((task) => (
                <div
                  key={task.id}
                  className="p-4 rounded-lg bg-slate-700/30 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{task.icon}</div>
                      <div>
                        <h4 className="text-slate-200 font-semibold text-sm">{task.title}</h4>
                        <p className="text-slate-400 text-xs">{task.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-right">
                        <p className="text-orange-400 font-bold text-sm">+{task.coins}</p>
                        <p className="text-slate-400 text-xs">coins</p>
                      </div>
                      {getStatusBadge(task.status)}
                    </div>
                  </div>
                  
                  {task.status === "available" && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-xs text-slate-400">
                        <span>Progress: {task.progress}/{task.maxProgress}</span>
                        <span>{Math.round((task.progress / task.maxProgress) * 100)}%</span>
                      </div>
                      <Progress value={(task.progress / task.maxProgress) * 100} className="h-2" />
                      <Button
                        onClick={() => completeTask(task.id)}
                        size="sm"
                        className="w-full bg-orange-600 hover:bg-orange-700"
                      >
                        <Play size={14} className="mr-2" />
                        Complete Task
                      </Button>
                    </div>
                  )}
                  
                  {task.status === "completed" && (
                    <div className="flex items-center gap-2 text-green-400 text-sm">
                      <CheckCircle size={16} />
                      Task completed!
                    </div>
                  )}
                  
                  {task.status === "locked" && (
                    <div className="flex items-center gap-2 text-slate-400 text-sm">
                      <Clock size={16} />
                      Requirements not met
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>

        {/* Rewards Section */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
        >
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Gift size={20} />
                Spend Coins
              </CardTitle>
              <CardDescription className="text-slate-400">
                Redeem your coins for exclusive rewards
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {rewards.map((reward) => (
                <div
                  key={reward.id}
                  className="p-4 rounded-lg bg-slate-700/30 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{reward.image}</div>
                      <div>
                        <h4 className="text-slate-200 font-semibold text-sm">{reward.name}</h4>
                        <p className="text-slate-400 text-xs">{reward.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-right">
                        <p className="text-orange-400 font-bold text-sm">{reward.coins}</p>
                        <p className="text-slate-400 text-xs">coins</p>
                      </div>
                      {getStatusBadge(reward.status)}
                    </div>
                  </div>
                  
                  {reward.status === "available" && (
                    <Button
                      onClick={() => purchaseReward(reward.id)}
                      size="sm"
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      disabled={coinBalance.available < reward.coins}
                    >
                      <Gift size={14} className="mr-2" />
                      {coinBalance.available < reward.coins ? 'Insufficient Coins' : 'Purchase Reward'}
                    </Button>
                  )}
                  
                  {reward.status === "purchased" && (
                    <div className="flex items-center gap-2 text-orange-400 text-sm">
                      <CheckCircle size={16} />
                      Reward purchased!
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
} 