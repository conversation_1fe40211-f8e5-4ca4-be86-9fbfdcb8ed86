"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { 
  BookOpen, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  DollarSign,
  TrendingUp,
  Shield,
  Users,
  FileText,
  Download,
  ExternalLink,
  ChevronDown,
  ChevronRight
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Separator } from "@/components/ui/separator"

interface Rule {
  id: string
  title: string
  description: string
  category: 'general' | 'trading' | 'risk' | 'compliance'
  importance: 'critical' | 'important' | 'standard'
  status: 'active' | 'pending' | 'deprecated'
}

interface RuleCategory {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  rules: Rule[]
}

export default function RulesPage() {
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null)

  const ruleCategories: RuleCategory[] = [
    {
      id: 'general',
      title: 'General Trading Rules',
      description: 'Basic rules and guidelines for all traders',
      icon: <BookOpen size={20} />,
      rules: [
        {
          id: '1',
          title: 'Account Verification',
          description: 'All traders must complete KYC verification before accessing live trading accounts. This includes providing valid government-issued ID and proof of address.',
          category: 'general',
          importance: 'critical',
          status: 'active'
        },
        {
          id: '2',
          title: 'Trading Hours',
          description: 'Trading is available 24/5 (Monday to Friday). Weekend trading is not permitted. All positions must be closed before market close on Friday.',
          category: 'general',
          importance: 'important',
          status: 'active'
        },
        {
          id: '3',
          title: 'Account Funding',
          description: 'Minimum account balance requirements must be maintained. Failure to meet minimum balance may result in account suspension.',
          category: 'general',
          importance: 'important',
          status: 'active'
        }
      ]
    },
    {
      id: 'trading',
      title: 'Trading Guidelines',
      description: 'Specific rules for trading activities and strategies',
      icon: <TrendingUp size={20} />,
      rules: [
        {
          id: '4',
          title: 'Position Sizing',
          description: 'Maximum position size is limited to 2% of account balance per trade. This rule applies to all instruments and timeframes.',
          category: 'trading',
          importance: 'critical',
          status: 'active'
        },
        {
          id: '5',
          title: 'Stop Loss Requirements',
          description: 'All positions must have a stop loss order placed within 30 minutes of opening. Stop loss must be at least 1% from entry price.',
          category: 'trading',
          importance: 'critical',
          status: 'active'
        },
        {
          id: '6',
          title: 'Maximum Daily Loss',
          description: 'Daily loss limit is set at 5% of account balance. Once reached, trading is suspended until the next trading day.',
          category: 'trading',
          importance: 'critical',
          status: 'active'
        },
        {
          id: '7',
          title: 'News Trading Restrictions',
          description: 'Trading during major economic news releases is restricted. Positions must be closed 30 minutes before and reopened 30 minutes after news events.',
          category: 'trading',
          importance: 'important',
          status: 'active'
        }
      ]
    },
    {
      id: 'risk',
      title: 'Risk Management',
      description: 'Risk management protocols and drawdown limits',
      icon: <Shield size={20} />,
      rules: [
        {
          id: '8',
          title: 'Maximum Drawdown',
          description: 'Maximum allowed drawdown is 10% of account balance. Exceeding this limit results in immediate account suspension and review.',
          category: 'risk',
          importance: 'critical',
          status: 'active'
        },
        {
          id: '9',
          title: 'Correlation Limits',
          description: 'Maximum 3 positions in correlated instruments (e.g., EUR/USD, GBP/USD, USD/CHF). This prevents over-exposure to similar market movements.',
          category: 'risk',
          importance: 'important',
          status: 'active'
        },
        {
          id: '10',
          title: 'Weekend Gap Risk',
          description: 'All positions must be closed before Friday market close to avoid weekend gap risk. Overnight positions are not permitted.',
          category: 'risk',
          importance: 'important',
          status: 'active'
        }
      ]
    },
    {
      id: 'compliance',
      title: 'Compliance & Ethics',
      description: 'Regulatory compliance and ethical trading practices',
      icon: <Users size={20} />,
      rules: [
        {
          id: '11',
          title: 'Market Manipulation',
          description: 'Any form of market manipulation, including spoofing, layering, or wash trading, is strictly prohibited and will result in immediate account termination.',
          category: 'compliance',
          importance: 'critical',
          status: 'active'
        },
        {
          id: '12',
          title: 'Insider Trading',
          description: 'Trading based on non-public information is prohibited. All traders must comply with insider trading regulations.',
          category: 'compliance',
          importance: 'critical',
          status: 'active'
        },
        {
          id: '13',
          title: 'Record Keeping',
          description: 'All trading activities must be properly documented. Traders must maintain records for a minimum of 5 years.',
          category: 'compliance',
          importance: 'important',
          status: 'active'
        }
      ]
    }
  ]

  const getImportanceBadge = (importance: string) => {
    const config = {
      critical: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: AlertTriangle },
      important: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: Clock },
      standard: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: CheckCircle }
    }

    const badgeConfig = config[importance as keyof typeof config]
    const Icon = badgeConfig.icon

    return (
      <Badge className={`${badgeConfig.color} border`}>
        <Icon size={12} className="mr-1" />
        {importance.charAt(0).toUpperCase() + importance.slice(1)}
      </Badge>
    )
  }

  const getStatusBadge = (status: string) => {
    const config = {
      active: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: CheckCircle },
      pending: { color: "bg-yellow-500/15 text-yellow-400 border-yellow-500/25", icon: Clock },
      deprecated: { color: "bg-red-500/15 text-red-400 border-red-500/25", icon: XCircle }
    }

    const badgeConfig = config[status as keyof typeof config]
    const Icon = badgeConfig.icon

    return (
      <Badge className={`${badgeConfig.color} border`}>
        <Icon size={12} className="mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Rules & Guidelines</h1>
          <p className="text-slate-400 mt-1">Comprehensive trading rules and compliance guidelines</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-800">
            <Download size={16} className="mr-2" />
            Download PDF
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <ExternalLink size={16} className="mr-2" />
            View Full Policy
          </Button>
        </div>
      </motion.div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Total Rules</p>
                <p className="text-2xl font-bold text-slate-100">
                  {ruleCategories.reduce((sum, cat) => sum + cat.rules.length, 0)}
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                <FileText size={24} className="text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Critical Rules</p>
                <p className="text-2xl font-bold text-red-400">
                  {ruleCategories.reduce((sum, cat) => 
                    sum + cat.rules.filter(rule => rule.importance === 'critical').length, 0
                  )}
                </p>
              </div>
              <div className="w-12 h-12 bg-red-500/15 rounded-lg flex items-center justify-center">
                <AlertTriangle size={24} className="text-red-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Active Rules</p>
                <p className="text-2xl font-bold text-green-400">
                  {ruleCategories.reduce((sum, cat) => 
                    sum + cat.rules.filter(rule => rule.status === 'active').length, 0
                  )}
                </p>
              </div>
              <div className="w-12 h-12 bg-green-500/15 rounded-lg flex items-center justify-center">
                <CheckCircle size={24} className="text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm font-medium">Categories</p>
                <p className="text-2xl font-bold text-slate-100">{ruleCategories.length}</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/15 rounded-lg flex items-center justify-center">
                <BookOpen size={24} className="text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rules Categories */}
      <div className="space-y-6">
        {ruleCategories.map((category, index) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-lg flex items-center justify-center border border-blue-500/30">
                      {category.icon}
                    </div>
                    <div>
                      <CardTitle className="text-slate-100">{category.title}</CardTitle>
                      <CardDescription className="text-slate-400">{category.description}</CardDescription>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setExpandedCategory(expandedCategory === category.id ? null : category.id)}
                    className="text-slate-400 hover:text-slate-200"
                  >
                    {expandedCategory === category.id ? <ChevronDown size={20} /> : <ChevronRight size={20} />}
                  </Button>
                </div>
              </CardHeader>
              
              {expandedCategory === category.id && (
                <CardContent>
                  <div className="space-y-4">
                    {category.rules.map((rule, ruleIndex) => (
                      <motion.div
                        key={rule.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: ruleIndex * 0.05 }}
                        className="p-4 rounded-lg bg-slate-700/30 border border-slate-600/50"
                      >
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="text-slate-100 font-semibold">{rule.title}</h4>
                          <div className="flex gap-2">
                            {getImportanceBadge(rule.importance)}
                            {getStatusBadge(rule.status)}
                          </div>
                        </div>
                        <p className="text-slate-300 text-sm leading-relaxed">{rule.description}</p>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Important Notice */}
      <Card className="bg-gradient-to-r from-red-500/10 to-orange-500/10 border-red-500/20">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
              <AlertTriangle size={20} className="text-red-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-100 mb-2">Important Notice</h3>
              <p className="text-red-200 text-sm leading-relaxed">
                All traders must read and understand these rules before engaging in any trading activities. 
                Violation of any critical rules may result in immediate account suspension or termination. 
                These rules are subject to change, and traders will be notified of any updates.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-slate-100">Need Help?</CardTitle>
          <CardDescription className="text-slate-400">
            Contact our support team for clarification on any rules or guidelines
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 rounded-lg bg-slate-700/30">
              <Mail size={20} className="text-blue-400 mx-auto mb-2" />
              <p className="text-slate-200 font-medium">Email Support</p>
              <p className="text-slate-400 text-sm"><EMAIL></p>
            </div>
            <div className="text-center p-4 rounded-lg bg-slate-700/30">
              <Clock size={20} className="text-green-400 mx-auto mb-2" />
              <p className="text-slate-200 font-medium">Response Time</p>
              <p className="text-slate-400 text-sm">Within 24 hours</p>
            </div>
            <div className="text-center p-4 rounded-lg bg-slate-700/30">
              <Users size={20} className="text-purple-400 mx-auto mb-2" />
              <p className="text-slate-200 font-medium">Live Chat</p>
              <p className="text-slate-400 text-sm">Available 24/7</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 