import { NextResponse } from 'next/server';
import { getBaseUrl } from '@/app/config/env';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    
    const response = await fetch(`${getBaseUrl()}/auth/login`, {
      method: 'POST',
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Authentication failed' },
        { status: response.status }
      );
    }

    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 