"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Shield, 
  Key, 
  Bell, 
  Globe, 
  Save,
  Camera,
  Edit3,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { toast } from "react-toastify"

interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  phone: string
  country: string
  city: string
  dateOfBirth: string
  joinDate: string
  avatar: string
  isEmailVerified: boolean
  isPhoneVerified: boolean
  isKYCVerified: boolean
  tradingExperience: string
  preferredCurrency: string
  timezone: string
}

interface NotificationSettings {
  emailNotifications: boolean
  smsNotifications: boolean
  pushNotifications: boolean
  tradeAlerts: boolean
  newsUpdates: boolean
  marketingEmails: boolean
}

export default function ProfilePage() {
  const [profile, setProfile] = useState<UserProfile>({
    id: "1",
    email: "<EMAIL>",
    firstName: "John",
    lastName: "Doe",
    phone: "+****************",
    country: "United States",
    city: "New York",
    dateOfBirth: "1990-05-15",
    joinDate: "2024-01-15",
    avatar: "",
    isEmailVerified: true,
    isPhoneVerified: false,
    isKYCVerified: true,
    tradingExperience: "intermediate",
    preferredCurrency: "USD",
    timezone: "America/New_York"
  })

  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    tradeAlerts: true,
    newsUpdates: false,
    marketingEmails: false
  })

  const [isEditing, setIsEditing] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [editedProfile, setEditedProfile] = useState<UserProfile>(profile)

  useEffect(() => {
    // Fetch user profile data from API
    fetchProfileData()
  }, [])

  const fetchProfileData = async () => {
    try {
      // Replace with actual API call
      // const response = await fetch('/api/profile')
      // const data = await response.json()
      // setProfile(data)
    } catch (error) {
      console.error('Error fetching profile:', error)
    }
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // Replace with actual API call
      // await fetch('/api/profile', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(editedProfile)
      // })
      
      setProfile(editedProfile)
      setIsEditing(false)
      toast.success('Profile updated successfully!')
    } catch (error) {
      toast.error('Failed to update profile')
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setEditedProfile(profile)
    setIsEditing(false)
  }

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    setEditedProfile(prev => ({ ...prev, [field]: value }))
  }

  const handleNotificationChange = (field: keyof NotificationSettings, value: boolean) => {
    setNotifications(prev => ({ ...prev, [field]: value }))
  }

  const VerificationBadge = ({ verified, type }: { verified: boolean, type: string }) => (
    <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-xs font-medium ${
      verified 
        ? 'bg-green-500/15 text-green-400 border border-green-500/25' 
        : 'bg-yellow-500/15 text-yellow-400 border border-yellow-500/25'
    }`}>
      {verified ? <CheckCircle size={14} /> : <AlertCircle size={14} />}
      {verified ? `${type} Verified` : `${type} Pending`}
    </div>
  )

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Profile Settings</h1>
          <p className="text-slate-400 mt-1">Manage your account information and preferences</p>
        </div>
        <div className="flex gap-3">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={handleCancel}
                className="border-slate-600 text-slate-300 hover:bg-slate-800"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                disabled={isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Edit3 size={16} className="mr-2" />
              Edit Profile
            </Button>
          )}
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <User size={20} />
                Basic Information
              </CardTitle>
              <CardDescription className="text-slate-400">
                Your personal and contact details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName" className="text-slate-300">First Name</Label>
                  <Input
                    id="firstName"
                    value={isEditing ? editedProfile.firstName : profile.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    disabled={!isEditing}
                    className="bg-slate-700/50 border-slate-600 text-slate-100"
                  />
                </div>
                <div>
                  <Label htmlFor="lastName" className="text-slate-300">Last Name</Label>
                  <Input
                    id="lastName"
                    value={isEditing ? editedProfile.lastName : profile.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    disabled={!isEditing}
                    className="bg-slate-700/50 border-slate-600 text-slate-100"
                  />
                </div>
                <div>
                  <Label htmlFor="email" className="text-slate-300">Email Address</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="email"
                      value={profile.email}
                      disabled
                      className="bg-slate-700/50 border-slate-600 text-slate-100"
                    />
                    <VerificationBadge verified={profile.isEmailVerified} type="Email" />
                  </div>
                </div>
                <div>
                  <Label htmlFor="phone" className="text-slate-300">Phone Number</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="phone"
                      value={isEditing ? editedProfile.phone : profile.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      disabled={!isEditing}
                      className="bg-slate-700/50 border-slate-600 text-slate-100"
                    />
                    <VerificationBadge verified={profile.isPhoneVerified} type="Phone" />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="country" className="text-slate-300">Country</Label>
                  <Select
                    value={isEditing ? editedProfile.country : profile.country}
                    onValueChange={(value) => handleInputChange('country', value)}
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      <SelectItem value="United States">United States</SelectItem>
                      <SelectItem value="United Kingdom">United Kingdom</SelectItem>
                      <SelectItem value="Canada">Canada</SelectItem>
                      <SelectItem value="Australia">Australia</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="city" className="text-slate-300">City</Label>
                  <Input
                    id="city"
                    value={isEditing ? editedProfile.city : profile.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    disabled={!isEditing}
                    className="bg-slate-700/50 border-slate-600 text-slate-100"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Trading Preferences */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Globe size={20} />
                Trading Preferences
              </CardTitle>
              <CardDescription className="text-slate-400">
                Configure your trading experience and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="experience" className="text-slate-300">Trading Experience</Label>
                  <Select
                    value={isEditing ? editedProfile.tradingExperience : profile.tradingExperience}
                    onValueChange={(value) => handleInputChange('tradingExperience', value)}
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      <SelectItem value="beginner">Beginner</SelectItem>
                      <SelectItem value="intermediate">Intermediate</SelectItem>
                      <SelectItem value="advanced">Advanced</SelectItem>
                      <SelectItem value="expert">Expert</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="currency" className="text-slate-300">Preferred Currency</Label>
                  <Select
                    value={isEditing ? editedProfile.preferredCurrency : profile.preferredCurrency}
                    onValueChange={(value) => handleInputChange('preferredCurrency', value)}
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-slate-700/50 border-slate-600 text-slate-100">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-700 border-slate-600">
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Profile Picture */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="relative inline-block">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    {profile.avatar ? (
                      <img src={profile.avatar} alt="Profile" className="w-24 h-24 rounded-full object-cover" />
                    ) : (
                      <User size={32} className="text-white" />
                    )}
                  </div>
                  {isEditing && (
                    <Button
                      size="sm"
                      variant="outline"
                      className="absolute -bottom-2 -right-2 w-8 h-8 p-0 rounded-full border-slate-600 bg-slate-700"
                    >
                      <Camera size={14} />
                    </Button>
                  )}
                </div>
                <h3 className="text-lg font-semibold text-slate-100">
                  {profile.firstName} {profile.lastName}
                </h3>
                <p className="text-slate-400 text-sm">Member since {new Date(profile.joinDate).toLocaleDateString()}</p>
                <VerificationBadge verified={profile.isKYCVerified} type="KYC" />
              </div>
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Bell size={20} />
                Notifications
              </CardTitle>
              <CardDescription className="text-slate-400">
                Manage your notification preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-200 text-sm font-medium">Email Notifications</p>
                  <p className="text-slate-400 text-xs">Receive updates via email</p>
                </div>
                <Switch
                  checked={notifications.emailNotifications}
                  onCheckedChange={(checked) => handleNotificationChange('emailNotifications', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-200 text-sm font-medium">SMS Notifications</p>
                  <p className="text-slate-400 text-xs">Receive updates via SMS</p>
                </div>
                <Switch
                  checked={notifications.smsNotifications}
                  onCheckedChange={(checked) => handleNotificationChange('smsNotifications', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-200 text-sm font-medium">Trade Alerts</p>
                  <p className="text-slate-400 text-xs">Get notified about trade events</p>
                </div>
                <Switch
                  checked={notifications.tradeAlerts}
                  onCheckedChange={(checked) => handleNotificationChange('tradeAlerts', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-200 text-sm font-medium">News Updates</p>
                  <p className="text-slate-400 text-xs">Receive market news and updates</p>
                </div>
                <Switch
                  checked={notifications.newsUpdates}
                  onCheckedChange={(checked) => handleNotificationChange('newsUpdates', checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Security */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Shield size={20} />
                Security
              </CardTitle>
              <CardDescription className="text-slate-400">
                Manage your account security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                <Key size={16} className="mr-2" />
                Change Password
              </Button>
              <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                <Shield size={16} className="mr-2" />
                Two-Factor Auth
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 