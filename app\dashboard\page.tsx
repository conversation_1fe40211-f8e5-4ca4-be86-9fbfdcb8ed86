"use client"

import { useState, useEffect, useRef } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Activity, 
  DollarSign, 
  Percent, 
  <PERSON><PERSON><PERSON><PERSON>gle,
  TrendingUp,
  TrendingDown,
  Users,
  Target,
  Clock,
  BarChart3,
  Shield,
  Award
} from "lucide-react"
import { useRouter } from 'next/navigation'
import { ResponsiveSankey } from '@nivo/sankey'
import { secureFetch, API_CONFIG } from '@/app/config/api'

import { Head<PERSON> } from "@/components/header"
import { OverviewCard } from "@/components/overview-card"
import { AccountDetailsCard } from "@/components/account-details-card"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Table, TableBody, TableCell, TableHead, TableHeader, TableRow 
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import TradingChallenge from '../component/trading-challenge'

interface TradeDetail {
  openTime: string
  closeTime: string
  symbol: string
  action: string
  sizing: {
    type: string
    value: string
  }
  openPrice: number
  closePrice: number
  profit: number
}

interface AccountDetail {
  balance: number
  equity: number
  totalTrades: number
  drawdown: number
  profit: number
}

interface FormattedTrade {
  id: string
  symbol: string
  type: string
  openPrice: number
  profit: number
  date: string
  volume: number
}

interface PerformanceData {
  period: string
  accountBalance: number
  portfolioEquity: number
}

interface DrawdownData {
  maxDrawdown: number
  currentDrawdown: number
}

interface Order {
  id: string;
  symbol: string;
  action: string;
  sizing: {
    type: string;
    value: string;
  };
  openPrice: number;
  closePrice: number;
  profit: number;
}

export default function DashboardPage() {
  const router = useRouter();
  const [accountDetails, setAccountDetails] = useState<AccountDetail | null>(null)
  const [tradeHistory, setTradeHistory] = useState<FormattedTrade[]>([])
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [drawdownData, setDrawdownData] = useState<DrawdownData>({
    maxDrawdown: 0,
    currentDrawdown: 0,
  })
  const [hasOrders, setHasOrders] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState('');
  const [terminalId, setTerminalId] = useState('');
  const [mounted, setMounted] = useState(false);
  const isMounted = useRef(true);

  // Check for authentication first
  // useEffect(() => {
  //   const token = localStorage.getItem('access_token');
  //   if (!token) {
  //     router.push('/login');
  //   }
  // }, [router]);

  useEffect(() => {
    setMounted(true);
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Check for orders
  useEffect(() => {
    const checkOrders = async () => {
      if (!mounted) return;

      try {
        setIsLoading(true);
        const token = localStorage.getItem('access_token');
        if (!token) {
          setError('No access token found');
          setHasOrders(false);
          return;
        }

        console.log('Fetching orders from API...');
        const response = await secureFetch('/order/order_ids');

        if (!response.ok) {
          console.error('Order fetch failed with status:', response.status);
          throw new Error(`Failed to fetch orders (Status: ${response.status})`);
        }

        const responseData = await response.json();
        console.log('Orders API response:', responseData);

        if (!isMounted.current) return;

        // Handle both wrapped and direct array responses
        let orders = [];
        if (responseData && responseData.data && Array.isArray(responseData.data)) {
          orders = responseData.data;
        } else if (Array.isArray(responseData)) {
          orders = responseData;
        } else {
          console.warn('Unexpected response format:', responseData);
          setHasOrders(false);
          return;
        }

        console.log('Processed orders:', orders);
        setHasOrders(orders.length > 0);

        if (orders.length > 0) {
          const firstOrder = orders[0];
          const orderId = firstOrder.id || firstOrder.order_id;
          console.log('Setting selected account ID:', orderId);
          if (orderId) {
            localStorage.setItem('selectedAccountId', orderId.toString());
          }
        } else {
          console.log('No orders found for user');
        }
      } catch (err) {
        console.error('Error checking orders:', err);
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('An error occurred while checking orders');
        }
        setHasOrders(false);
      } finally {
        if (isMounted.current) {
          setIsLoading(false);
        }
      }
    };

    checkOrders();
  }, [mounted]);

  // Initialize session and terminal IDs
  useEffect(() => {
    if (mounted) {
      const storedSessionId = localStorage.getItem('session_id');
      const storedTerminalId = localStorage.getItem('terminal_id');
      
      if (storedSessionId) setSessionId(storedSessionId);
      if (storedTerminalId) setTerminalId(storedTerminalId);
    }
  }, [mounted]);

  // Fetch data when we have orders
  useEffect(() => {
    if (hasOrders && mounted) {
      fetchData();
    }
  }, [hasOrders, mounted]);

  const LoadingSpinner = () => (
    <div className="min-h-screen flex items-center justify-center">
      <motion.div
        animate={{ 
          scale: [1, 1.2, 1],
          rotate: [0, 360]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full"
      />
    </div>
  );

  const fetchData = async () => {
    try {
      if (!isMounted.current) return;

      const formData = new FormData()
      formData.append('session', sessionId)
      formData.append('account_id', terminalId)

      console.log('Fetching MyFXBook data with:', { sessionId, terminalId });

      const response = await secureFetch(API_CONFIG.endpoints.myfxbookFetch, {
        method: 'POST',
        body: formData
      })

      if (!isMounted.current) return;

      if (!response.ok) {
        console.warn('MyFXBook fetch failed, setting default values');
        setAccountDetails({
          balance: 0,
          equity: 0,
          totalTrades: 0,
          drawdown: 0,
          profit: 0,
        });
        setTradeHistory([]);
        setDrawdownData({
          maxDrawdown: 0,
          currentDrawdown: 0,
        });
        setIsLoading(false);
        return;
      }

      const responseData = await response.json()
      const data = responseData.data || responseData;

      console.log('MyFXBook data received:', data);

      if (!isMounted.current) return;

      setAccountDetails({
        balance: data.account_info?.balance || 0,
        equity: data.account_info?.equity || 0,
        totalTrades: data.history?.length || 0,
        drawdown: data.account_info?.drawdown || 0,
        profit: data.account_info?.profit || 0,
      });

      const formattedTrades = (data.history || []).map((trade: TradeDetail) => ({
        id: trade.openTime || '',
        symbol: trade.symbol || '',
        type: trade.action || '',
        openPrice: trade.openPrice || 0,
        profit: trade.profit || 0,
        date: trade.openTime || '',
        volume: trade.sizing?.value || 0
      }));

      if (!isMounted.current) return;

      setTradeHistory(formattedTrades);
      setDrawdownData({
        maxDrawdown: data.account_info?.drawdown || 0,
        currentDrawdown: data.account_info?.drawdown || 0,
      });
      setIsLoading(false);
    } catch (error) {
      console.error('Error fetching MyFXBook data:', error);
      if (!isMounted.current) return;

      setAccountDetails({
        balance: 0,
        equity: 0,
        totalTrades: 0,
        drawdown: 0,
        profit: 0,
      });
      setTradeHistory([]);
      setDrawdownData({
        maxDrawdown: 0,
        currentDrawdown: 0,
      });
      setIsLoading(false);
    }
  }

  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    const handleStorageChange = () => {
      if (!mounted || !isMounted.current) return;
      
      const newSessionId = localStorage.getItem('session_id') || '';
      const newTerminalId = localStorage.getItem('terminal_id') || '';
      
      if (newSessionId !== sessionId) {
        setSessionId(newSessionId);
      }
      if (newTerminalId !== terminalId) {
        setTerminalId(newTerminalId);
      }
    };

    if (mounted) {
      window.addEventListener('storage', handleStorageChange);
      interval = setInterval(handleStorageChange, 1000);
    }

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      clearInterval(interval);
    };
  }, [sessionId, terminalId, mounted]);

  if (!mounted) {
    return null;
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  // Only show trading challenge if we explicitly know there are no orders
  if (hasOrders === false) {
    return <TradingChallenge isDashboard={true} />;
  }

  const formatBalance = (balance?: number) => {
    return balance ? `$${balance.toLocaleString()}` : '$0'
  }

  const calculateDrawdown = (drawdown?: number) => {
    return drawdown ? `${drawdown.toFixed(2)}%` : '0%'
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-red-400 text-center">
          <p className="text-xl font-semibold">Error loading dashboard</p>
          <p className="mt-2">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Trading Dashboard</h1>
          <p className="text-slate-400 mt-1">Monitor your trading performance and account status</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge className="bg-green-500/15 text-green-400 border-green-500/25">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
            Live Trading
          </Badge>
        </div>
      </motion.div>

      {/* Account Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Account Balance</p>
                  <p className="text-2xl font-bold text-slate-100">{formatBalance(accountDetails?.balance)}</p>
                  <p className="text-xs text-slate-500 mt-1">Current balance</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                  <DollarSign size={24} className="text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Total Profit</p>
                  <p className={`text-2xl font-bold ${(accountDetails?.profit || 0) >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {formatBalance(accountDetails?.profit)}
                  </p>
                  <p className="text-xs text-slate-500 mt-1">All time profit</p>
                </div>
                <div className="w-12 h-12 bg-green-500/15 rounded-lg flex items-center justify-center">
                  <TrendingUp size={24} className="text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Total Trades</p>
                  <p className="text-2xl font-bold text-slate-100">{accountDetails?.totalTrades || 0}</p>
                  <p className="text-xs text-slate-500 mt-1">Completed trades</p>
                </div>
                <div className="w-12 h-12 bg-purple-500/15 rounded-lg flex items-center justify-center">
                  <BarChart3 size={24} className="text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Current Drawdown</p>
                  <p className="text-2xl font-bold text-red-400">{calculateDrawdown(drawdownData.currentDrawdown)}</p>
                  <p className="text-xs text-slate-500 mt-1">Risk level</p>
                </div>
                <div className="w-12 h-12 bg-red-500/15 rounded-lg flex items-center justify-center">
                  <AlertTriangle size={24} className="text-red-400" />
                </div>
            </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Account Details */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.5 }}
          className="lg:col-span-2"
        >
          <AccountDetailsCard
            orderId={mounted ? localStorage.getItem('selectedAccountId') || "" : ""}
          />
        </motion.div>

        {/* Quick Stats & Actions */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.6 }}
          className="space-y-6"
        >
          {/* Performance Summary */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Target size={20} />
                Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 rounded-lg bg-slate-700/30">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-500/15 rounded-lg flex items-center justify-center">
                    <TrendingUp size={16} className="text-blue-400" />
                  </div>
                  <div>
                    <p className="text-slate-200 text-sm font-medium">Win Rate</p>
                    <p className="text-xs text-slate-400">Last 30 days</p>
                  </div>
                </div>
                <p className="text-lg font-bold text-green-400">68.5%</p>
              </div>
              
              <div className="flex items-center justify-between p-3 rounded-lg bg-slate-700/30">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-purple-500/15 rounded-lg flex items-center justify-center">
                    <Clock size={16} className="text-purple-400" />
                  </div>
                  <div>
                    <p className="text-slate-200 text-sm font-medium">Avg Hold Time</p>
                    <p className="text-xs text-slate-400">Per trade</p>
                  </div>
                </div>
                <p className="text-lg font-bold text-slate-100">2.4h</p>
              </div>
              
              <div className="flex items-center justify-between p-3 rounded-lg bg-slate-700/30">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-orange-500/15 rounded-lg flex items-center justify-center">
                    <Shield size={16} className="text-orange-400" />
                  </div>
                  <div>
                    <p className="text-slate-200 text-sm font-medium">Risk Score</p>
                    <p className="text-xs text-slate-400">Current level</p>
                  </div>
                </div>
                <p className="text-lg font-bold text-yellow-400">Medium</p>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100 flex items-center gap-2">
                <Activity size={20} />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {tradeHistory.slice(0, 3).map((trade, index) => (
                <div key={trade.id} className="flex items-center justify-between p-3 rounded-lg bg-slate-700/30">
      <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      trade.type === 'Buy' ? 'bg-green-500/15' : 'bg-red-500/15'
                    }`}>
                      {trade.type === 'Buy' ? (
                        <TrendingUp size={16} className="text-green-400" />
                      ) : (
                        <TrendingDown size={16} className="text-red-400" />
                      )}
                    </div>
                    <div>
                      <p className="text-slate-200 text-sm font-medium">{trade.symbol}</p>
                      <p className="text-xs text-slate-400">{trade.type}</p>
                    </div>
                  </div>
                  <p className={`text-sm font-semibold ${trade.profit >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                    {trade.profit >= 0 ? '+' : ''}{formatBalance(trade.profit)}
                  </p>
        </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-slate-100">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full bg-blue-600 hover:bg-blue-700">
                <BarChart3 size={16} className="mr-2" />
                View Full History
              </Button>
              <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                <Award size={16} className="mr-2" />
                Performance Report
              </Button>
              <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                <Users size={16} className="mr-2" />
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}