"use client"
import React, { useState } from 'react';
import { ChevronDown, ChevronUp, HelpCircle, Sparkles, Search, BookOpen, Target, Shield, Wallet, Clock, AlertTriangle, Zap, Layers, StepForward } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const hftNeoCategories = [
    {
        title: "Challenge Phase Rules",
        icon: Target,
        faqs: [
            {
                question: "What are the Challenge Phase Trading Objectives?",
                answer: `• Profit Target: 8%
• Daily Drawdown: 5%
• Overall Drawdown Limit: 10%
• Minimum Trading Days: 0`
            },
            {
                question: "What are the Challenge Phase Trading Permissions?",
                answer: `• News Trading: Allowed
• Overnight Holding: Allowed
• Weekend Holding: Prohibited – All trades must be closed before market closes on Friday
• HFT Bots: Allowed during challenge phase
• Minimum Trade Duration: All trades must be held for at least 2 minutes before closing`
            }
        ]
    },
    {
        title: "Funded Phase Rules",
        icon: Shield,
        faqs: [
            {
                question: "What are the Funded Phase Trading Restrictions?",
                answer: `• Expert Advisors (EA) Prohibited: No automated trading systems allowed
• Hedging: Only allowed within the same account, cross-account hedging prohibited
• Weekend Holding: Strictly prohibited (Hard Breach)
• Trading Layers: Maximum 2 layers allowed, exceeding results in account breach
• Martingale Strategy: Not allowed
• Minimum Trade Duration: 2 minutes minimum hold time`
            }
        ]
    },
    {
        title: "Profit & Risk Rules",
        icon: Wallet,
        faqs: [
            {
                question: "What is the 35% Maximum Trade Profit Rule?",
                answer: `• No single trade can exceed 35% of total period profits
• Trades within 2-minute window count as one trade
• Daily payout limit: 35% maximum from single trade
• Exceeding limits may result in profit reduction or trade adjustments`
            },
            {
                question: "How does the Lot Size Consistency Rule work?",
                answer: `• Calculate your average lot size: Total lots ÷ Total trades
• Minimum allowed = Average × 0.25
• Maximum allowed = Average × 2.00
• Example: For 2 lot average
  - Minimum: 0.5 lots
  - Maximum: 4 lots
• Trades outside range excluded from withdrawal calculations
• Multiple trades within 2 minutes count as one position`
            }
        ]
    },
    {
        title: "Payouts & Withdrawals",
        icon: Clock,
        faqs: [
            {
                question: "How are profits distributed?",
                answer: `• Profit share ranges from 50% to 90%
• Withdrawals processed bi-weekly
• Final amount based on trader performance and agreement terms
• Trades must follow lot size consistency rules
• All trading rules must be followed for withdrawal eligibility`
            }
        ]
    }
];

const oneStepCategories = [
    {
        title: "Challenge Phase Rules",
        icon: Target,
        faqs: [
            {
                question: "What are the Challenge Phase Trading Objectives?",
                answer: `• Profit Target: 10%
• Daily Drawdown: 4%
• Overall Drawdown Limit: 10%
• Minimum Trading Days: 5`
            },
            {
                question: "What are the Challenge Phase Trading Permissions?",
                answer: `• News Trading: Allowed
• Overnight Holding: Allowed
• Weekend Holding: Prohibited – Trades must be closed before market closes on Friday
• EA (Expert Advisor): Allowed during Phase 1
• HFT Bots: Not Allowed during Phase 1
• Minimum Trade Duration: All trades must be held for at least 2 minutes before closing`
            }
        ]
    },
    {
        title: "Funded Phase Rules",
        icon: Shield,
        faqs: [
            {
                question: "What are the Funded Phase Trading Restrictions?",
                answer: `• Expert Advisors (EA) Prohibited: No automated trading systems allowed
• Hedging: Only allowed within the same account, cross-account hedging prohibited
• Weekend Holding: Strictly prohibited (Hard Breach)
• Trading Layers: Maximum 2 layers allowed, exceeding results in account breach
• Martingale Strategy: Not allowed
• Minimum Trade Duration: 2 minutes minimum hold time`
            }
        ]
    },
    {
        title: "Profit & Risk Rules",
        icon: Wallet,
        faqs: [
            {
                question: "What is the 35% Maximum Trade Profit Rule?",
                answer: `1. Maximum Trade Profit Rule:
• No single trade can exceed 35% of total period profits
• Applies to traders who passed the 1-Step ALGO
• Ensures consistent strategy over relying on few big trades

2. Two-Minute Window Rule:
• Trades within 2 minutes count as one trade for 35% limit

3. Daily Payout Limit:
• Maximum 35% profit from single trade per day
• Promotes steady performance

4. Exceeding Limits:
• Penalties may include profit reduction
• Trade count adjustments based on policies
• Encourages balanced trading approach`
            }
        ]
    },
    {
        title: "Payouts & Withdrawals",
        icon: Clock,
        faqs: [
            {
                question: "How are profits distributed?",
                answer: `• Profit share ranges from 50% to 80%
• Withdrawals processed bi-weekly
• Final amount based on trader performance
• Must follow all trading rules for eligibility`
            }
        ]
    }
];

const twoStepCategories = [
    {
        title: "Challenge Phase Rules",
        icon: Target,
        faqs: [
            {
                question: "What are the Challenge Phase Trading Objectives?",
                answer: `• Profit Target First Phase: 10%
• Profit Target Second Phase: 5%
• Daily Drawdown: 5%
• Overall Drawdown Limit: 10%
• Minimum Trading Days: 5`
            },
            {
                question: "What are the Challenge Phase Trading Permissions?",
                answer: `• News Trading: Allowed
• Overnight Holding: Allowed
• Weekend Holding: Prohibited – Trades must be closed before market closes on Friday
• EA (Expert Advisor): Allowed
• HFT Bots: Not Allowed
• Minimum Trade Duration: All trades must be held for at least 2 minutes before closing`
            }
        ]
    },
    {
        title: "Funded Phase Rules",
        icon: Shield,
        faqs: [
            {
                question: "What are the Funded Phase Trading Restrictions?",
                answer: `• Expert Advisors (EA) Prohibited: No automated trading systems allowed
• Hedging: Only allowed within the same account, cross-account hedging prohibited
• Weekend Holding: Strictly prohibited (Hard Breach)
• Trading Layers: Maximum 2 layers allowed, exceeding results in account breach
• Martingale Strategy: Not allowed
• Minimum Trade Duration: 2 minutes minimum hold time`
            }
        ]
    },
    {
        title: "Profit & Risk Rules",
        icon: Wallet,
        faqs: [
            {
                question: "What are the profit allocation and withdrawal rules?",
                answer: `• Profit share ranges from 50% to 80%
• Withdrawals processed bi-weekly
• 6% Profit Cap per Payout Cycle: Maximum withdrawal of 6% profit per cycle
• Ensures controlled and steady growth
• All trading rules must be followed for withdrawal eligibility

These guidelines maintain a fair, disciplined, and sustainable trading environment for all Two-Step traders.`
            }
        ]
    }
];

const FaqSection: React.FC = () => {
    const [activeTab, setActiveTab] = useState<'hft' | 'onestep' | 'twostep'>('hft');
    const [expandedCategories, setExpandedCategories] = useState<number[]>([]);
    const [expandedQuestions, setExpandedQuestions] = useState<{ [key: number]: number[] }>({});
    const [searchQuery, setSearchQuery] = useState('');

    const handleTabChange = (tab: 'hft' | 'onestep' | 'twostep') => {
        setActiveTab(tab);
        setExpandedCategories([]);
        setExpandedQuestions({});
    };

    const handleCategoryToggle = (categoryIndex: number) => {
        setExpandedCategories(prev =>
            prev.includes(categoryIndex)
                ? prev.filter(i => i !== categoryIndex)
                : [...prev, categoryIndex]
        );
    };

    const handleQuestionToggle = (categoryIndex: number, questionIndex: number) => {
        setExpandedQuestions(prev => {
            const categoryQuestions = prev[categoryIndex] || [];
            return {
                ...prev,
                [categoryIndex]: categoryQuestions.includes(questionIndex)
                    ? categoryQuestions.filter(i => i !== questionIndex)
                    : [...categoryQuestions, questionIndex]
            };
        });
    };

    const currentCategories = activeTab === 'hft' ? hftNeoCategories : activeTab === 'onestep' ? oneStepCategories : twoStepCategories;

    return (
        <section className="py-20 px-4 md:px-8 lg:px-12 relative overflow-hidden bg-[#0A1426]">
            {/* Enhanced Background Elements */}
            <div className="absolute inset-0">
                <div className="absolute inset-0 bg-gradient-to-b from-blue-950/30 via-[#0F1A2E]/40 to-[#121E36]/50" />
            <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-5" />
                <div className="absolute top-0 right-0 w-[500px] h-[500px] bg-orange-500/5 rounded-full blur-[100px]" />
                <div className="absolute bottom-0 left-0 w-[500px] h-[500px] bg-blue-500/5 rounded-full blur-[100px]" />
            </div>
            
            <div className="max-w-7xl mx-auto relative z-10">
                {/* Enhanced Header */}
                    <motion.div
                    initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                    className="text-center mb-16"
                >
                    <div className="inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20 backdrop-blur-sm mb-6">
                        <HelpCircle className="w-8 h-8 text-orange-400" />
                    </div>
                    <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
                        Frequently Asked Questions
                    </h2>
                    <p className="text-gray-400 text-lg md:text-xl max-w-3xl mx-auto leading-relaxed">
                        Everything you need to know about our trading programs and rules
                    </p>
                    </motion.div>
                    
                {/* Enhanced Search Bar */}
                <motion.div 
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="max-w-2xl mx-auto mb-16"
                >
                    <div className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 to-blue-500/20 rounded-2xl blur-md transition-all duration-300 opacity-0 group-hover:opacity-100" />
                        <div className="relative">
                            <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 transition-colors duration-300 group-hover:text-orange-400" />
                            <input
                                type="text"
                                placeholder="Search your question..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full py-5 pl-14 pr-5 rounded-2xl bg-white/5 border border-white/10 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300 backdrop-blur-sm"
                            />
                        </div>
                    </div>
                </motion.div>

                {/* Enhanced Program Tabs */}
                <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="flex flex-wrap justify-center gap-4 mb-16"
                >
                    {[
                        { id: 'hft', label: 'HFT Neo', icon: Zap },
                        { id: 'onestep', label: 'One-Step', icon: StepForward },
                        { id: 'twostep', label: 'Two-Step', icon: Layers }
                    ].map(tab => (
                        <button
                            key={tab.id}
                            onClick={() => handleTabChange(tab.id as 'hft' | 'onestep' | 'twostep')}
                            className={`px-8 py-4 rounded-xl text-base font-medium transition-all duration-300 flex items-center gap-2 ${
                                activeTab === tab.id
                                    ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg shadow-orange-500/20 scale-105'
                                    : 'bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white hover:scale-105'
                            }`}
                        >
                            {React.createElement(tab.icon, { 
                                className: `w-4 h-4 ${activeTab === tab.id ? 'text-white' : 'text-gray-400'}`
                            })}
                            {tab.label}
                        </button>
                    ))}
                </motion.div>

                {/* Enhanced FAQ Categories */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {currentCategories.map((category, categoryIndex) => (
                        <motion.div
                            key={categoryIndex}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.3, delay: categoryIndex * 0.1 }}
                            className="group bg-gradient-to-b from-white/[0.08] to-white/[0.02] rounded-2xl border border-white/10 overflow-hidden backdrop-blur-sm hover:border-orange-500/20 transition-all duration-300"
                        >
                            {/* Enhanced Category Header */}
                            <button
                                onClick={() => handleCategoryToggle(categoryIndex)}
                                className="w-full px-6 py-5 flex items-center justify-between text-left transition-all duration-300 hover:bg-white/5 group"
                            >
                                <div className="flex items-center gap-4">
                                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-orange-500/20 to-orange-600/10 flex items-center justify-center border border-orange-500/20 group-hover:scale-110 transition-transform duration-300">
                                        {React.createElement(category.icon, { className: "w-6 h-6 text-orange-400" })}
                                    </div>
                                    <h3 className="text-xl font-semibold text-white group-hover:text-orange-400 transition-colors duration-300">{category.title}</h3>
                                </div>
                                {React.createElement(expandedCategories.includes(categoryIndex) ? ChevronUp : ChevronDown, {
                                    className: "w-6 h-6 text-gray-400 group-hover:text-orange-400 transition-colors duration-300"
                                })}
                            </button>
                            
                            {/* Enhanced Questions */}
                            <AnimatePresence>
                                {expandedCategories.includes(categoryIndex) && (
                                    <motion.div
                                        initial={{ height: 0 }}
                                        animate={{ height: "auto" }}
                                        exit={{ height: 0 }}
                                        transition={{ duration: 0.3 }}
                                        className="overflow-hidden"
                                    >
                                        <div className="px-6 py-4 space-y-4">
                                            {category.faqs.map((faq, questionIndex) => (
                                                <div
                                                    key={questionIndex}
                                                    className="border-b border-white/5 last:border-0 pb-4 last:pb-0"
                                                >
                                                    <button
                                                        onClick={() => handleQuestionToggle(categoryIndex, questionIndex)}
                                                        className="w-full flex items-center justify-between text-left group py-2"
                                                    >
                                                        <span className="text-gray-200 group-hover:text-white transition-colors duration-300 flex-1 pr-4">
                                                            {faq.question}
                                                        </span>
                                                        {React.createElement(
                                                            expandedQuestions[categoryIndex]?.includes(questionIndex)
                                                                ? ChevronUp
                                                                : ChevronDown,
                                                            {
                                                                className: "w-5 h-5 text-gray-400 group-hover:text-orange-400 transition-colors duration-300 flex-shrink-0"
                                                            }
                                                        )}
                                                </button>
                                                <AnimatePresence>
                                                        {expandedQuestions[categoryIndex]?.includes(questionIndex) && (
                                                        <motion.div
                                                            initial={{ height: 0, opacity: 0 }}
                                                            animate={{ height: "auto", opacity: 1 }}
                                                            exit={{ height: 0, opacity: 0 }}
                                                            transition={{ duration: 0.2 }}
                                                                className="overflow-hidden"
                                                        >
                                                                <div className="mt-4 text-gray-400 whitespace-pre-line text-sm leading-relaxed bg-gradient-to-br from-white/5 to-transparent p-6 rounded-xl border border-white/5">
                                                                {faq.answer}
                                                                </div>
                                                        </motion.div>
                                                    )}
                                                </AnimatePresence>
                                            </div>
                                        ))}
                                        </div>
                                    </motion.div>
                                )}
                            </AnimatePresence>
                        </motion.div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default FaqSection;
