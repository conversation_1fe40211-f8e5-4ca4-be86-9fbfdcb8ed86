import { headers } from 'next/headers';
import { getBaseUrl } from '@/app/config/env';
import { createSecureResponse } from '@/app/lib/api-helpers';

export async function GET() {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return createSecureResponse({
        message: 'Unauthorized',
        status: 401
      });
    }

    const response = await fetch(`${getBaseUrl()}/order/failed_orders`, {
      headers: {
        'Authorization': authHeader,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    if (!response.ok) {
      return createSecureResponse({
        message: 'Failed to fetch orders',
        status: response.status
      });
    }

    return createSecureResponse({
      data,
      status: 200
    });
  } catch (error) {
    return createSecureResponse({
      message: 'Internal server error',
      status: 500
    });
  }
} 