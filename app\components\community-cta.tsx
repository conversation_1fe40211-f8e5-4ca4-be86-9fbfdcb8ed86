import { But<PERSON> } from "@/components/ui/button"
import { Facebook, Instagram, Send, ExternalLink, Twitter } from 'lucide-react'
import { DiscordLogoIcon } from "@radix-ui/react-icons"
import Link from 'next/link'
import { motion } from "framer-motion"
import { Inter } from "next/font/google"

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
})

export const CommunityCallToAction = () => {
  return (
    <div className="relative min-h-[500px] w-full bg-[#0A0F1C] py-16 sm:py-24">
      {/* Background elements */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-transparent to-orange-900/10" />

      <div className="relative container mx-auto px-4">
        <div className="max-w-6xl mx-auto text-center">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-5xl sm:text-7xl font-bold mb-6"
          >
            <span className="text-white">Get </span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-orange-500 to-blue-600">
              Social
            </span>
            <span className="text-white"> with Our Herd</span>
          </motion.h2>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
            className="max-w-3xl mx-auto mb-12"
          >
            <p className="text-gray-300 text-xl mb-4">
              Join our vibrant <span className="text-white font-semibold">community</span> for discussions, support, and idea-sharing.
            </p>
            <p className="text-gray-300 text-xl">
              Let's navigate the markets together and <span className="text-white font-semibold">maximize</span> our investment potential!
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16"
          >
            <Link href="https://discord.gg/ZzG8demuuz" target="_blank" rel="noopener noreferrer">
              <Button className="bg-gradient-to-r from-orange-500 to-blue-600 hover:from-orange-600 hover:to-blue-700 text-white px-8 py-6 rounded-lg font-bold transition-all duration-300 flex items-center gap-2">
                <DiscordLogoIcon className="w-5 h-5" />
                Join our Discord for exclusive content & promotions
              </Button>
            </Link>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="flex flex-wrap justify-center items-center gap-6"
          >
            {[
              {
                Icon: Send,
                link: "https://t.me/fundedhorizon",
                label: "Telegram",
                className: "hover:text-blue-400"
              },
              {
                Icon: Twitter,
                link: "https://twitter.com/fundedhorizon",
                label: "Twitter",
                className: "hover:text-blue-400"
              },
              {
                Icon: Instagram,
                link: "https://www.instagram.com/fundedhorizon",
                label: "Instagram",
                className: "hover:text-pink-400"
              },
              {
                Icon: Facebook,
                link: "https://www.facebook.com/fundedhorizon1",
                label: "Facebook",
                className: "hover:text-blue-500"
              },
              {
                Icon: DiscordLogoIcon,
                link: "https://discord.gg/ZzG8demuuz",
                label: "Discord",
                className: "hover:text-indigo-400"
              }
            ].map(({ Icon, link, label, className }) => (
              <motion.a
                key={label}
                href={link}
                target="_blank"
                rel="noopener noreferrer"
                className={`transform hover:scale-110 transition-all duration-300 ${className}`}
                whileHover={{ y: -5 }}
              >
                <Icon className="w-8 h-8 text-white" />
              </motion.a>
            ))}
          </motion.div>
        </div>
      </div>
    </div>
  )
} 