import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { getBaseUrl } from '@/app/config/env';

export async function POST(request: Request) {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    
    const response = await fetch(`${getBaseUrl()}/order/order`, {
      method: 'POST',
      headers: {
        'Authorization': authHeader,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        { error: errorText },
        { status: response.status }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 