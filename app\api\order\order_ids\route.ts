import { headers } from 'next/headers';
import { getBaseUrl } from '@/app/config/env';
import { createSecureResponse } from '@/app/lib/api-helpers';

export async function GET() {
  try {
    const headersList = headers();
    const authHeader = headersList.get('Authorization');

    if (!authHeader) {
      return createSecureResponse({
        message: 'Unauthorized',
        status: 401
      });
    }

    const response = await fetch(`${getBaseUrl()}/order/order_ids`, {
      headers: {
        'Authorization': authHeader,
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    const responseData = await response.json();

    if (!response.ok) {
      return createSecureResponse({
        message: 'Failed to fetch order IDs',
        status: response.status
      });
    }

    // Ensure we're returning the data in the expected format
    return createSecureResponse({
      data: Array.isArray(responseData) ? responseData : [],
      status: 200
    });
  } catch (error) {
    return createSecureResponse({
      message: 'Internal server error',
      status: 500
    });
  }
} 