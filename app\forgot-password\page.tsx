"use client";
import { useState, ChangeEvent, FormEvent } from "react";

// This tells Next.js that this page should be dynamically rendered
export const dynamic = 'force-dynamic';
import { Navbar } from "../component/navbar";
import Link from "next/link";
import { motion } from "framer-motion";
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

import { Bitcoin, DollarSign } from 'lucide-react';

interface ForgotPasswordData {
  email: string;
}

interface InputFieldProps {
  label: string;
  type: string;
  placeholder: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLInputElement>) => void;
  name: string;
  className?: string;
}

function InputField({
  label,
  type,
  placeholder,
  value,
  onChange,
  name,
  className = "",
}: InputFieldProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="flex flex-col gap-2"
    >
      <label className="text-sm font-medium text-gray-300">{label}</label>
      <input
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        name={name}
        className={`px-4 py-3 rounded-lg bg-black/50 text-white border border-gray-700 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-300 ${className}`}
      />
    </motion.div>
  );
}

const FloatingIcons = () => {
  const [dimensions, setDimensions] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1000,
    height: typeof window !== 'undefined' ? window.innerHeight : 800
  });

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          initial={{
            x: Math.random() * dimensions.width,
            y: Math.random() * dimensions.height,
            scale: 0.5 + Math.random() * 0.5,
            opacity: 0.3 + Math.random() * 0.4
          }}
          animate={{
            x: [null, Math.random() * dimensions.width],
            y: [null, Math.random() * dimensions.height],
            rotate: [0, 360]
          }}
          transition={{
            duration: 15 + Math.random() * 10,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute text-gray-600"
        >
          {i % 3 === 0 ? (
            <Bitcoin size={24} />
          ) : (
            <DollarSign size={24} />
          )}
        </motion.div>
      ))}
    </div>
  );
};

export default function ForgotPassword() {
  const [forgotPasswordData, setForgotPasswordData] = useState<ForgotPasswordData>({
    email: "",
  });
  const [loading, setLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setForgotPasswordData({ ...forgotPasswordData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Store email in localStorage for reset password page
      if (typeof window !== 'undefined') {
        localStorage.setItem('reset_email', forgotPasswordData.email);
      }

      // Construct URL with query parameters
      const url = `https://fundedhorizon-back-65a0759eedf9.herokuapp.com/auth/forgot-password?email=${encodeURIComponent(forgotPasswordData.email)}`;

      const response = await fetch(url, {
        method: "POST",
      });

      if (response.ok) {
        toast.success('Password reset link sent to your email!');
        // Set emailSent to true to show success message
        setEmailSent(true);
      } else {
        console.error("Password reset request failed");
        toast.error('Failed to process your request. Please try again.');
      }
    } catch (error) {
      console.error("Password reset request failed:", error);
      toast.error('Network error. Please check your connection.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Navbar />
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-gray-800 to-blue-900 p-4 md:p-6 lg:p-8 relative">
        <FloatingIcons />
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="bg-gradient-to-br from-gray-900 to-black rounded-2xl shadow-2xl p-6 md:p-8 lg:p-10 w-full max-w-md mt-20 mx-auto border border-gray-800 relative z-10"
        >
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 mb-8 text-center"
          >
            Forgot Password
          </motion.h1>

          {emailSent ? (
            <div className="text-center space-y-6">
              <div className="bg-green-900/30 border border-green-500/50 rounded-lg p-4 mb-6">
                <p className="text-green-400 font-medium">Password reset link sent!</p>
                <p className="text-gray-400 mt-2">Please check your email inbox for instructions to reset your password.</p>
              </div>
              <p className="text-gray-400">
                Didn't receive the email? Check your spam folder or try again.
              </p>
              <button
                onClick={() => setEmailSent(false)}
                className="text-orange-400 hover:text-orange-500 hover:underline transition-colors duration-300"
              >
                Try again
              </button>
            </div>
          ) : (
            <>
              <p className="text-gray-400 text-center mb-6">
                Enter your email address and we'll send you a link to reset your password.
              </p>

              <form onSubmit={handleSubmit} className="space-y-6">
                <InputField
                  label="Email"
                  type="email"
                  placeholder="Enter your email"
                  value={forgotPasswordData.email}
                  onChange={handleChange}
                  name="email"
                />

                <motion.div
                  whileHover={{ scale: loading ? 1 : 1.02 }}
                  whileTap={{ scale: loading ? 1 : 0.98 }}
                >
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-orange-400 to-orange-500 text-white font-bold py-4 rounded-lg hover:from-orange-500 hover:to-orange-600 transition-all duration-300 shadow-lg hover:shadow-orange-500/25 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {loading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      'Send Reset Instructions'
                    )}
                  </button>
                </motion.div>
              </form>
            </>
          )}

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
            className="mt-6 text-center space-y-3"
          >
            <p className="text-gray-400">
              Remember your password?{" "}
              <Link href="/sigin" className="text-orange-400 hover:text-orange-500 hover:underline transition duration-300">
                Sign In
              </Link>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
}
