"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  Gift, 
  Clock, 
  Users, 
  Award,
  Star,
  Calendar,
  DollarSign,
  TrendingUp,
  CheckCircle,
  Play,
  Zap,
  Target,
  BarChart3,
  Trophy,
  Sparkles
} from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { toast } from "react-toastify"

interface Giveaway {
  id: string
  title: string
  description: string
  prize: string
  prizeValue: number
  participants: number
  maxParticipants: number
  endDate: string
  status: 'active' | 'upcoming' | 'ended' | 'won'
  category: 'trading' | 'social' | 'referral' | 'achievement'
  requirements: string[]
  image: string
  isParticipating: boolean
  userRank?: number
}

interface GiveawayStats {
  totalParticipated: number
  totalWon: number
  activeGiveaways: number
  totalPrizes: number
  winRate: number
}

export default function GiveawayPage() {
  const [giveaways, setGiveaways] = useState<Giveaway[]>([])
  const [stats, setStats] = useState<GiveawayStats>({
    totalParticipated: 0,
    totalWon: 0,
    activeGiveaways: 0,
    totalPrizes: 0,
    winRate: 0
  })
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchGiveawayData()
  }, [])

  const fetchGiveawayData = async () => {
    try {
      setIsLoading(true)
      // Mock data - replace with actual API call
      const mockGiveaways: Giveaway[] = [
        {
          id: "1",
          title: "Weekly Trading Challenge",
          description: "Complete the most profitable trades this week and win big!",
          prize: "$1,000 Trading Account",
          prizeValue: 1000,
          participants: 245,
          maxParticipants: 500,
          endDate: "2024-03-25T23:59:59Z",
          status: "active",
          category: "trading",
          requirements: ["Minimum 10 trades", "70% win rate", "Active account"],
          image: "🏆",
          isParticipating: true,
          userRank: 15
        },
        {
          id: "2",
          title: "Social Media Contest",
          description: "Share your trading journey and get featured on our social media",
          prize: "Premium Trading Course + $500",
          prizeValue: 800,
          participants: 89,
          maxParticipants: 200,
          endDate: "2024-03-30T23:59:59Z",
          status: "active",
          category: "social",
          requirements: ["Share on social media", "Use #FundedHorizon", "Tag 3 friends"],
          image: "📱",
          isParticipating: false
        },
        {
          id: "3",
          title: "Referral Master Challenge",
          description: "Refer the most friends and earn exclusive rewards",
          prize: "VIP Trading Account + $2,000",
          prizeValue: 2500,
          participants: 156,
          maxParticipants: 300,
          endDate: "2024-04-05T23:59:59Z",
          status: "active",
          category: "referral",
          requirements: ["Refer 5+ friends", "All referrals must be active", "Complete KYC"],
          image: "👥",
          isParticipating: true,
          userRank: 8
        },
        {
          id: "4",
          title: "Achievement Unlocked",
          description: "Reach new trading milestones and unlock special rewards",
          prize: "Custom Trading Setup + $1,500",
          prizeValue: 2000,
          participants: 67,
          maxParticipants: 100,
          endDate: "2024-04-10T23:59:59Z",
          status: "upcoming",
          category: "achievement",
          requirements: ["Complete all achievements", "Maintain 80% win rate", "Trade for 30 days"],
          image: "🎯",
          isParticipating: false
        },
        {
          id: "5",
          title: "Monthly Champion",
          description: "Become the top trader of the month",
          prize: "Luxury Trading Retreat + $5,000",
          prizeValue: 8000,
          participants: 0,
          maxParticipants: 50,
          endDate: "2024-04-30T23:59:59Z",
          status: "upcoming",
          category: "trading",
          requirements: ["Highest profit percentage", "Minimum 100 trades", "No violations"],
          image: "👑",
          isParticipating: false
        }
      ]

      setGiveaways(mockGiveaways)
      
      // Calculate stats
      const totalParticipated = mockGiveaways.filter(g => g.isParticipating).length
      const totalWon = 2 // Mock data
      const activeGiveaways = mockGiveaways.filter(g => g.status === 'active').length
      const totalPrizes = mockGiveaways.reduce((sum, g) => sum + g.prizeValue, 0)
      const winRate = totalParticipated > 0 ? (totalWon / totalParticipated) * 100 : 0

      setStats({
        totalParticipated,
        totalWon,
        activeGiveaways,
        totalPrizes,
        winRate
      })
    } catch (error) {
      console.error('Error fetching giveaway data:', error)
      toast.error('Failed to load giveaway data')
    } finally {
      setIsLoading(false)
    }
  }

  const participateInGiveaway = async (giveawayId: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setGiveaways(prev => prev.map(g => 
        g.id === giveawayId 
          ? { ...g, isParticipating: true, participants: g.participants + 1 }
          : g
      ))
      
      toast.success('Successfully joined the giveaway!')
    } catch (error) {
      toast.error('Failed to join giveaway')
    }
  }

  const leaveGiveaway = async (giveawayId: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setGiveaways(prev => prev.map(g => 
        g.id === giveawayId 
          ? { ...g, isParticipating: false, participants: g.participants - 1 }
          : g
      ))
      
      toast.success('Left the giveaway successfully')
    } catch (error) {
      toast.error('Failed to leave giveaway')
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      active: { color: "bg-green-500/15 text-green-400 border-green-500/25", icon: Play },
      upcoming: { color: "bg-blue-500/15 text-blue-400 border-blue-500/25", icon: Clock },
      ended: { color: "bg-slate-500/15 text-slate-400 border-slate-500/25", icon: Award },
      won: { color: "bg-orange-500/15 text-orange-400 border-orange-500/25", icon: Trophy }
    }

    const badgeConfig = config[status as keyof typeof config]
    const Icon = badgeConfig.icon

    return (
      <Badge className={`${badgeConfig.color} border`}>
        <Icon size={12} className="mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getCategoryIcon = (category: string) => {
    const icons = {
      trading: "📈",
      social: "📱",
      referral: "👥",
      achievement: "🎯"
    }
    return icons[category as keyof typeof icons] || "🎁"
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatTimeRemaining = (endDate: string) => {
    const now = new Date()
    const end = new Date(endDate)
    const diff = end.getTime() - now.getTime()
    
    if (diff <= 0) return "Ended"
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (days > 0) return `${days}d ${hours}h`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  const filteredGiveaways = selectedCategory === "all" 
    ? giveaways 
    : giveaways.filter(g => g.category === selectedCategory)

  if (isLoading) {
    return (
      <div className="p-6 flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-400">Loading giveaways...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Giveaways</h1>
          <p className="text-slate-400 mt-1">Participate in exciting contests and win amazing prizes</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge className="bg-orange-500/15 text-orange-400 border-orange-500/25">
            <Gift size={14} className="mr-1" />
            {stats.activeGiveaways} Active
          </Badge>
        </div>
      </motion.div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Participated</p>
                  <p className="text-2xl font-bold text-blue-400">{stats.totalParticipated}</p>
                  <p className="text-xs text-slate-500 mt-1">Total contests</p>
                </div>
                <div className="w-12 h-12 bg-blue-500/15 rounded-lg flex items-center justify-center">
                  <Users size={24} className="text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Won</p>
                  <p className="text-2xl font-bold text-orange-400">{stats.totalWon}</p>
                  <p className="text-xs text-slate-500 mt-1">Prizes claimed</p>
                </div>
                <div className="w-12 h-12 bg-orange-500/15 rounded-lg flex items-center justify-center">
                  <Trophy size={24} className="text-orange-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Win Rate</p>
                  <p className="text-2xl font-bold text-green-400">{stats.winRate.toFixed(1)}%</p>
                  <p className="text-xs text-slate-500 mt-1">Success rate</p>
                </div>
                <div className="w-12 h-12 bg-green-500/15 rounded-lg flex items-center justify-center">
                  <TrendingUp size={24} className="text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <Card className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm font-medium">Total Prizes</p>
                  <p className="text-2xl font-bold text-purple-400">{formatCurrency(stats.totalPrizes)}</p>
                  <p className="text-xs text-slate-500 mt-1">Available value</p>
                </div>
                <div className="w-12 h-12 bg-purple-500/15 rounded-lg flex items-center justify-center">
                  <DollarSign size={24} className="text-purple-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Giveaways List */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-slate-100 flex items-center gap-2">
                  <Gift size={20} />
                  Available Giveaways
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Join exciting contests and win amazing prizes
                </CardDescription>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedCategory === "all" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("all")}
                  className={selectedCategory === "all" ? "bg-orange-600" : "border-slate-600 text-slate-300"}
                >
                  All
                </Button>
                <Button
                  variant={selectedCategory === "trading" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("trading")}
                  className={selectedCategory === "trading" ? "bg-blue-600" : "border-slate-600 text-slate-300"}
                >
                  Trading
                </Button>
                <Button
                  variant={selectedCategory === "social" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("social")}
                  className={selectedCategory === "social" ? "bg-green-600" : "border-slate-600 text-slate-300"}
                >
                  Social
                </Button>
                <Button
                  variant={selectedCategory === "referral" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory("referral")}
                  className={selectedCategory === "referral" ? "bg-purple-600" : "border-slate-600 text-slate-300"}
                >
                  Referral
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {filteredGiveaways.length === 0 ? (
              <div className="text-center py-8 text-slate-400">
                No giveaways found
              </div>
            ) : (
              filteredGiveaways.map((giveaway) => (
                <div
                  key={giveaway.id}
                  className="p-6 rounded-lg bg-slate-700/30 border border-slate-600/50 hover:border-slate-500/50 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-4">
                      <div className="text-3xl">{giveaway.image}</div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-slate-200 font-semibold text-lg">{giveaway.title}</h3>
                          {getStatusBadge(giveaway.status)}
                        </div>
                        <p className="text-slate-400 text-sm mb-3">{giveaway.description}</p>
                        
                        <div className="flex items-center gap-6 text-sm">
                          <div className="flex items-center gap-2">
                            <DollarSign size={16} className="text-orange-400" />
                            <span className="text-slate-300 font-medium">{giveaway.prize}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users size={16} className="text-blue-400" />
                            <span className="text-slate-300">{giveaway.participants}/{giveaway.maxParticipants}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock size={16} className="text-green-400" />
                            <span className="text-slate-300">{formatTimeRemaining(giveaway.endDate)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-orange-400 font-bold text-lg mb-1">
                        {formatCurrency(giveaway.prizeValue)}
                      </div>
                      {giveaway.userRank && (
                        <Badge className="bg-blue-500/15 text-blue-400 border-blue-500/25">
                          Rank #{giveaway.userRank}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-xs text-slate-400">
                      <span>Participation Progress</span>
                      <span>{Math.round((giveaway.participants / giveaway.maxParticipants) * 100)}%</span>
                    </div>
                    <Progress value={(giveaway.participants / giveaway.maxParticipants) * 100} className="h-2" />
                    
                    <div className="space-y-2">
                      <h4 className="text-slate-300 font-medium text-sm">Requirements:</h4>
                      <ul className="space-y-1">
                        {giveaway.requirements.map((req, idx) => (
                          <li key={idx} className="text-slate-400 text-xs flex items-center gap-2">
                            <Star size={12} className="text-orange-400" />
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div className="flex gap-3">
                      {giveaway.status === 'active' && !giveaway.isParticipating && (
                        <Button
                          onClick={() => participateInGiveaway(giveaway.id)}
                          className="bg-orange-600 hover:bg-orange-700"
                        >
                          <Play size={16} className="mr-2" />
                          Join Giveaway
                        </Button>
                      )}
                      
                      {giveaway.status === 'active' && giveaway.isParticipating && (
                        <Button
                          onClick={() => leaveGiveaway(giveaway.id)}
                          variant="outline"
                          className="border-red-500/50 text-red-400 hover:bg-red-500/10"
                        >
                          Leave Giveaway
                        </Button>
                      )}
                      
                      {giveaway.status === 'upcoming' && (
                        <Button
                          variant="outline"
                          className="border-slate-600 text-slate-300"
                          disabled
                        >
                          <Clock size={16} className="mr-2" />
                          Coming Soon
                        </Button>
                      )}
                      
                      <Button
                        variant="outline"
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
} 