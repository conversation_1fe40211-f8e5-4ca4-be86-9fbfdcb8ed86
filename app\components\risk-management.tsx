"use client"

import { motion } from 'framer-motion'
import {
  Shield,
  BarChart4,
  Clock,
  TrendingDown,
  Lock,
  Sliders,
  <PERSON><PERSON>hart,
  Timer,
  Wallet,
  Sparkles
} from 'lucide-react'

export function RiskManagement() {
  const rules = [
    {
      title: "Daily Drawdown",
      description: "Maximum 5% loss allowed in a single trading day",
      icon: BarChart4,
      color: "blue"
    },
    {
      title: "Max Drawdown",
      description: "Overall account drawdown limited to 10%",
      icon: TrendingDown,
      color: "indigo"
    },
    {
      title: "Profit Target",
      description: "8-10% profit target depending on challenge type",
      icon: Pie<PERSON><PERSON>,
      color: "purple"
    },
    {
      title: "Leverage Limits",
      description: "Maximum leverage of 1:100 on major pairs",
      icon: Sliders,
      color: "blue"
    },
    {
      title: "Min Trading Days",
      description: "Minimum 5 trading days required for most challenges",
      icon: Clock,
      color: "indigo"
    },
    {
      title: "Profit Split",
      description: "Up to 90% profit sharing on funded accounts",
      icon: Wallet,
      color: "purple"
    }
  ]

  return (
    <section className="relative py-20 bg-gradient-to-b from-gray-950 via-blue-950/30 to-gray-950 overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[url('/grid.svg')] opacity-10" />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-950/20 via-transparent to-purple-950/10" />
        <motion.div
          className="absolute w-[500px] h-[500px] -right-64 -bottom-64 bg-blue-600/10 rounded-full blur-[120px]"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{ duration: 8, repeat: Infinity }}
        />
        <motion.div
          className="absolute w-[400px] h-[400px] -left-64 -top-64 bg-purple-600/10 rounded-full blur-[100px]"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{ duration: 10, repeat: Infinity }}
        />
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-400 via-white to-purple-400 bg-clip-text text-transparent mb-6">
            Risk Management Rules
          </h2>
          <p className="text-lg text-blue-200/80 max-w-3xl mx-auto">
            Our comprehensive risk management framework ensures sustainable trading success and account protection
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {rules.map((rule, index) => {
            // Helper functions for dynamic styles
            const getGradient = () => {
              switch(rule.color) {
                case 'purple': return 'from-purple-900/70 to-purple-800/50';
                case 'blue': return 'from-blue-900/70 to-blue-800/50';
                case 'indigo': return 'from-indigo-900/70 to-indigo-800/50';
                default: return 'from-gray-900/70 to-gray-800/50';
              }
            };

            const getIconColor = () => {
              switch(rule.color) {
                case 'purple': return 'text-purple-300';
                case 'blue': return 'text-blue-300';
                case 'indigo': return 'text-indigo-300';
                default: return 'text-gray-300';
              }
            };

            const getBorderGlow = () => {
              switch(rule.color) {
                case 'purple': return 'group-hover:shadow-purple-500/30';
                case 'blue': return 'group-hover:shadow-blue-500/30';
                case 'indigo': return 'group-hover:shadow-indigo-500/30';
                default: return 'group-hover:shadow-gray-500/30';
              }
            };

            const getGlowAccent = () => {
              switch(rule.color) {
                case 'purple': return 'from-purple-500/20 via-purple-400/10 to-transparent';
                case 'blue': return 'from-blue-500/20 via-blue-400/10 to-transparent';
                case 'indigo': return 'from-indigo-500/20 via-indigo-400/10 to-transparent';
                default: return 'from-gray-500/20 via-gray-400/10 to-transparent';
              }
            };

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.95 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                viewport={{ once: true }}
                transition={{
                  delay: index * 0.1,
                  duration: 0.5,
                  ease: "easeOut"
                }}
                whileHover={{
                  y: -8,
                  scale: 1.03,
                  transition: { duration: 0.3 }
                }}
                className={`group relative overflow-hidden rounded-2xl bg-gradient-to-br ${getGradient()} backdrop-blur-md border border-white/10 shadow-lg hover:shadow-xl ${getBorderGlow()} transition-all duration-300`}
              >
                {/* Glass effect overlay */}
                <div className="absolute inset-0 bg-zinc-950/70 backdrop-blur-[2px] pointer-events-none" />

                {/* Subtle gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-zinc-950/50 pointer-events-none" />

                {/* Accent color edge */}
                <div className={`absolute top-0 left-0 w-full h-1 bg-gradient-to-r ${getGlowAccent()} opacity-70 group-hover:opacity-100 transition-opacity duration-300`} />

                {/* Glow effect on hover */}
                <div className={`absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-500 bg-gradient-to-r ${getGlowAccent()} blur-xl pointer-events-none`} />

                {/* Card content */}
                <div className="relative z-10 p-7">
                  <div className="flex items-start gap-5">
                    {/* Icon with animation */}
                    <motion.div
                      className={`p-4 rounded-2xl bg-zinc-950/90 shadow-lg backdrop-blur-md border border-white/10 flex items-center justify-center relative overflow-hidden`}
                      whileHover={{
                        scale: 1.15,
                        rotate: [0, 5, -5, 0],
                        transition: { duration: 0.4 }
                      }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{
                        delay: index * 0.1 + 0.2,
                        duration: 0.4,
                        ease: "easeOut"
                      }}
                    >
                      {/* Icon background glow */}
                      <div className={`absolute inset-0 opacity-30 bg-gradient-to-br ${getGlowAccent()} blur-sm`} />
                      <rule.icon className={`w-5 h-5 ${getIconColor()} drop-shadow-md relative z-10`} />
                    </motion.div>

                    {/* Text content */}
                    <div className="flex-1">
                      <motion.h3
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 + 0.3, duration: 0.4 }}
                        className="text-lg font-bold text-white mb-2.5 group-hover:text-blue-200 transition-colors duration-300 tracking-tight"
                      >
                        {rule.title}
                      </motion.h3>

                      <motion.p
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 + 0.4, duration: 0.4 }}
                        className="text-gray-300 text-sm leading-relaxed font-light"
                      >
                        {rule.description}
                      </motion.p>
                    </div>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-white/10 to-transparent rounded-tl-3xl pointer-events-none" />
                  <div className="absolute top-0 right-0 w-12 h-12 bg-gradient-to-bl from-white/5 to-transparent rounded-bl-3xl pointer-events-none" />
                </div>
              </motion.div>
            );
          })}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6, duration: 0.5 }}
          whileHover={{
            scale: 1.02,
            transition: { duration: 0.3 }
          }}
          className="mt-16 p-8 bg-gradient-to-r from-blue-950/80 to-indigo-900/60 rounded-2xl border border-blue-500/30 shadow-xl max-w-3xl mx-auto text-center relative overflow-hidden backdrop-blur-md"
        >
          {/* Glass effect overlay */}
          <div className="absolute inset-0 bg-white/5 backdrop-blur-[2px] pointer-events-none" />

          {/* Top accent border */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/50 via-purple-500/50 to-blue-500/50 opacity-80" />

          {/* Subtle animated gradient overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-blue-500/15 to-purple-500/15 pointer-events-none"
            animate={{
              backgroundPosition: ['0% 0%', '100% 100%'],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              repeatType: 'reverse'
            }}
          />

          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-white/10 to-transparent rounded-br-3xl pointer-events-none" />
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-gradient-to-tl from-white/10 to-transparent rounded-tl-3xl pointer-events-none" />

          {/* Sparkle effects */}
          <motion.div
            className="absolute top-12 right-12 opacity-30"
            animate={{
              scale: [0.8, 1.2, 0.8],
              opacity: [0.2, 0.5, 0.2],
              rotate: [0, 15, 0]
            }}
            transition={{ duration: 5, repeat: Infinity }}
          >
            <Sparkles className="w-6 h-6 text-blue-300" />
          </motion.div>

          <motion.div
            className="absolute bottom-12 left-12 opacity-30"
            animate={{
              scale: [1, 0.8, 1],
              opacity: [0.3, 0.6, 0.3],
              rotate: [0, -15, 0]
            }}
            transition={{ duration: 4, repeat: Infinity, delay: 1 }}
          >
            <Sparkles className="w-5 h-5 text-purple-300" />
          </motion.div>

          <motion.div
            whileHover={{
              scale: 1.1,
              rotate: [0, -5, 5, 0],
              transition: { duration: 0.5 }
            }}
            className="relative z-10"
          >
            <Shield className="w-16 h-16 text-blue-300 mx-auto mb-5 drop-shadow-lg" />
          </motion.div>

          <div className="relative z-10">
            <h3 className="text-2xl font-bold text-white mb-4 tracking-tight">Your Security is Our Priority</h3>
            <p className="text-blue-100/90 leading-relaxed font-light">
              Our risk management rules are designed to protect your capital and ensure long-term trading success.
              All traders must adhere to these guidelines to maintain their funded accounts.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  )
}